package com.fintax.material.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fintax.api.act.entity.ManageEntity;
import com.fintax.api.act.feign.CapitalFlowFeignService;
import com.fintax.common.constant.FileConstant;
import com.fintax.common.utils.DateUtils;
import com.fintax.material.dao.AnalysisedFileMapper;
import com.fintax.material.dao.TPurchaseInvoiceBackendDao;
import com.fintax.material.dao.TSaleInvoiceBackendDao;
import com.fintax.material.entity.*;
import com.fintax.material.service.IAnalysisedFileService;
import com.fintax.material.service.IPurchaseInvoiceService;
import com.fintax.material.service.ISaleInvoiceService;
import com.fintax.material.utils.HanlpCompanyMatcher;
import com.fintax.material.utils.SimilarityChecker;
import com.fintax.material.utils.decompress.DecompressFileFactory;
import com.fintax.material.utils.XMLInvoiceExtractor;
import com.fintax.material.utils.decompress.PureJavaArchiveExtractor;
import com.fintax.material.utils.pdf.CompanyMatcher;
import com.fintax.material.utils.pdf.InvoicePdfParser;
import com.fintax.material.utils.pdf.PdfInvoiceExtractor;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import java.io.File;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

/**
 * 文件识别Service业务层处理
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@SuppressWarnings("all")
@Transactional
public class AnalysisedFileServiceImpl extends ServiceImpl<AnalysisedFileMapper, AnalysisedFile> implements IAnalysisedFileService
{
    private final AnalysisedFileMapper analysisedFileMapper;
    private final IPurchaseInvoiceService purchaseInvoiceService;
    private final ISaleInvoiceService saleInvoiceService;
    private final CapitalFlowFeignService capitalFlowFeignService;

    private final TPurchaseInvoiceBackendDao tPurchaseInvoiceBackendDao;
    private final TSaleInvoiceBackendDao tSaleInvoiceBackendDao;

    private static Lock lock = new ReentrantLock(true);
    public SimpleDateFormat noLineDateFmt = new SimpleDateFormat("yyyyMMdd");
    public SimpleDateFormat dateTimeFmt = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    /**
     * 查询文件识别
     *
     * @param id 文件识别主键
     * @return 文件识别
     */
    @Override
    public AnalysisedFile selectAnalysisedFileById(Long id)
    {
        return analysisedFileMapper.selectAnalysisedFileById(id);
    }

    /**
     * 查询文件识别列表
     *
     * @param analysisedFile 文件识别
     * @return 文件识别
     */
    @Override
    public List<AnalysisedFile> selectAnalysisedFileList(AnalysisedFile analysisedFile)
    {
        return analysisedFileMapper.selectAnalysisedFileList(analysisedFile);
    }

    /**
     * 新增文件识别
     *
     * @param analysisedFile 文件识别
     * @return 结果
     */
    @Override
    public int insertAnalysisedFile(AnalysisedFile analysisedFile)
    {
        analysisedFile.setCreateTime(DateUtils.getNowDate());
        return analysisedFileMapper.insertAnalysisedFile(analysisedFile);
    }

    /**
     * 修改文件识别
     *
     * @param analysisedFile 文件识别
     * @return 结果
     */
    @Override
    public int updateAnalysisedFile(AnalysisedFile analysisedFile)
    {
        analysisedFile.setUpdateTime(DateUtils.getNowDate());
        return analysisedFileMapper.updateAnalysisedFile(analysisedFile);
    }

    /**
     * 批量删除文件识别
     *
     * @param ids 需要删除的文件识别主键
     * @return 结果
     */
    @Override
    public int deleteAnalysisedFileByIds(Long[] ids)
    {
        return analysisedFileMapper.deleteAnalysisedFileByIds(ids);
    }

    /**
     * 删除文件识别信息
     *
     * @param id 文件识别主键
     * @return 结果
     */
    @Override
    public int deleteAnalysisedFileById(Long id)
    {
        return analysisedFileMapper.deleteAnalysisedFileById(id);
    }


    public AnalysisedFile parseExcelAndAssembleFileInfo(File file,ReceivedFile receivedFile,List<ManageEntity> accountList ,
                                                        Map<Integer, Map<String, Object>> headMap, Map<Integer, Map<String, Object>> dataMap){
        lock.lock();
        AnalysisedFile  analysisedFile = new AnalysisedFile();
        try {

            analysisedFile.setAttachid(receivedFile.getId());
            analysisedFile.setOriginPath(receivedFile.getFilePath());
            analysisedFile.setOriginName(file.getName());
            analysisedFile.setFileType(FileUtil.extName(file).toLowerCase());
            String newName = RandomUtil.randomString(20)+"."+FileUtil.getSuffix(file);
            analysisedFile.setNewName(newName);
            analysisedFile.setFileSize(file.length());
            analysisedFile.setReceiveDate(dateTimeFmt.format(new Date()));

            String newFilePath = "";
            Invoice invoice = new Invoice();
            analysisedFile.setFileType(FileUtil.extName(file).toLowerCase());
            List<Map<Integer, Object>> headList = (List<Map<Integer, Object>>) dataMap.get(0).get("headList");
            List<Map<Integer, Object>> dataList = (List<Map<Integer, Object>>) dataMap.get(0).get("dataList");
            Map<Integer, Object> headOne = new LinkedHashMap<>();
            if(headList.get(0).containsValue("单位社保费职工全险种申报明细")){//社保数据表格比较特殊，需要单独处理下。
                headOne = headList.get(2);
                dataList.add(headList.get(4));
            }else{
                headOne = (Map<Integer, Object>) headMap.get(0).get("head");
            }
            if (headOne == null) {
                analysisedFile.setState("error");
                analysisedFile.setMessage("无法识别的文件，没有读取到表头");
                return analysisedFile;
            }
            dataList.removeIf(s -> s.containsValue("合计行") || s.containsValue("合计"));
            if (dataList.isEmpty()) {
                analysisedFile.setState("error");
                analysisedFile.setMessage("无法识别的文件，没有读取到数据");
                return analysisedFile;
            }
            //有四种excle表格  1：采购发票  2：销售开票   3：采购发票认证清单  4：出纳明细
            if (headOne.containsValue("发票代码") && headOne.containsValue("发票号码") && headOne.containsValue("金额")
                    && headOne.containsValue("税额") && headOne.containsValue("开票人") && headOne.containsValue("销方名称")
                    && headOne.containsValue("购买方名称")) {
                //销售发票或者采购收票
                int buyerColIndex = 0;
                int sellerColIndex = 0;
                int dateColIndex = 0;
                Iterator<Map.Entry<Integer, Object>> iterator = headOne.entrySet().iterator();
                while (iterator.hasNext()) {
                    Map.Entry<Integer, Object> next = iterator.next();
                    if ("销方名称".equals(next.getValue())) {
                        sellerColIndex = next.getKey();
                    } else if ("购买方名称".equals(next.getValue())) {
                        buyerColIndex = next.getKey();
                    }else if("开票日期".equals(next.getValue())){
                        dateColIndex = next.getKey();
                    }
                }

                String dateStr =   (String) dataList.get(0).get(dateColIndex);
                String accountperiod = DateUtils.convertOne2AnotherDateStr(dateStr, "yyyy-MM");
                if(!StringUtils.isEmpty(accountperiod)){
                    analysisedFile.setAccountperiod(accountperiod);
                }


                String buyer0Name = (String) dataList.get(0).get(buyerColIndex);
                String buyerMiddleName = (String) dataList.get(dataList.size()/2).get(buyerColIndex);
                String buyerLastName = (String) dataList.get(dataList.size() - 1).get(buyerColIndex);

                String seller0Name = (String) dataList.get(0).get(sellerColIndex);
                String sellerMiddleName = (String) dataList.get(dataList.size()/2).get(sellerColIndex);
                String sellerLastName = (String) dataList.get(dataList.size() - 1).get(sellerColIndex);

                if (dataList.size() == 1) {
                    invoice.setBuyerName(buyer0Name);
                    invoice.setSellerName(seller0Name);
                    analysisedFile = getAccountInfoByInvoice(invoice, analysisedFile, accountList,dataList);
                } else if (
                        (Convert.toDBC(buyer0Name).equals(Convert.toDBC(buyerLastName))
                        && Convert.toDBC(buyer0Name).equals(Convert.toDBC(buyerMiddleName))
                                && Convert.toDBC(buyerLastName).equals(Convert.toDBC(buyerMiddleName))
                        )

                        && (!Convert.toDBC(seller0Name).equals(Convert.toDBC(sellerLastName))
                            || !Convert.toDBC(seller0Name).equals(Convert.toDBC(sellerMiddleName) )
                                || !Convert.toDBC(sellerLastName).equals(Convert.toDBC(sellerMiddleName) )
                )
                ) {//购方名称相同，销方名称不同 为采购收票
                    invoice.setBuyerName(Convert.toDBC(buyer0Name.trim()));
                    invoice.setSellerName("");
                    analysisedFile.setUploadType(FileConstant.UPLOAD_TYPE_INVOICE);

                    analysisedFile = getAccountInfoByInvoice(invoice, analysisedFile, accountList,dataList);
                }else if (
                        (Convert.toDBC(seller0Name).equals(Convert.toDBC(sellerLastName))
                        && Convert.toDBC(seller0Name).equals(Convert.toDBC(sellerMiddleName))
                                && Convert.toDBC(sellerLastName).equals(Convert.toDBC(sellerMiddleName))
                        )
                    && (!Convert.toDBC(buyer0Name).equals(Convert.toDBC(buyerLastName))
                                || !Convert.toDBC(buyer0Name).equals(Convert.toDBC(buyerMiddleName))
                                || !Convert.toDBC(buyerLastName).equals(Convert.toDBC(buyerMiddleName))
                        )
                ) {//销方名称相同，购方名称不同  为销售开票

                    invoice.setBuyerName("");
                    invoice.setSellerName(Convert.toDBC(seller0Name.trim()));
                    analysisedFile.setUploadType(FileConstant.UPLOAD_TYPE_MAKEBILL);
                    analysisedFile = getAccountInfoByInvoice(invoice, analysisedFile, accountList,dataList);
                }else if(Convert.toDBC(seller0Name).equals(Convert.toDBC(sellerLastName))
                        && Convert.toDBC(seller0Name).equals(Convert.toDBC(sellerMiddleName))
                        && Convert.toDBC(sellerLastName).equals(Convert.toDBC(sellerMiddleName))

                        && Convert.toDBC(buyer0Name).equals(Convert.toDBC(buyerLastName))
                        && Convert.toDBC(buyer0Name).equals(Convert.toDBC(buyerMiddleName))
                        && Convert.toDBC(buyerLastName).equals(Convert.toDBC(buyerMiddleName))
                ){//都相同，去账套内判断哦。
                    invoice.setBuyerName(buyer0Name.trim());
                    invoice.setSellerName(seller0Name.trim());
                    analysisedFile = getAccountInfoByInvoice(invoice, analysisedFile, accountList,dataList);
                }

                else{
                    analysisedFile.setState("error");
                    analysisedFile.setMessage("无法识别的文件！");
                    return analysisedFile;
                }


            }else{//未知文件
                    analysisedFile.setState("error");
                    analysisedFile.setMessage("无法识别的文件！");
                    return analysisedFile;
                }
            if (analysisedFile.getAccountCode() != null && analysisedFile.getMenuCode() != null) {
                     newFilePath = FileConstant.DOWNLOAD_BASEPATH + analysisedFile.getAccountCode() + FileConstant.SEPERATOR + analysisedFile.getMenuCode()
                            + FileConstant.SEPERATOR + noLineDateFmt.format(new Date()) + FileConstant.SEPERATOR + newName;
                analysisedFile.setFilePath(newFilePath);
            }
        } catch (Exception e) {
            analysisedFile.setState("error");
            analysisedFile.setMessage("上传文件发生未知错误！");
            e.printStackTrace();
        }finally {
            lock.unlock();
        }
        return analysisedFile;
    }

    @Override
    public List<AnalysisedFile> parseCompressFileInfo(String filePath, List<ManageEntity> allAccountList) {
        List<AnalysisedFile> list = new ArrayList<>();
        String fileName2 = FileUtil.getPrefix(filePath);
        String fileName = FileUtil.getName(filePath);

        try {
            lock.lock();
            File file = new File(filePath);
            PureJavaArchiveExtractor extractor = new PureJavaArchiveExtractor();
            List<String> files = extractor.extractArchive(filePath);
            for(String depressFilePath : files){
                //如果还是压缩包 则递归解压
                String ext1 = FileUtil.extName(depressFilePath).toLowerCase();
                if (!"pdf".equals(ext1) && !"ofd".equals(ext1) && !"xml".equals(ext1)) {
                   continue;
                }
                List<AnalysisedFile> analysisedFiles = analysisPDFXML(depressFilePath,allAccountList);
                if(!analysisedFiles.isEmpty()){
                    list.addAll(analysisedFiles);
                }

            }

        }catch (Exception e){
            e.printStackTrace();
        }finally {
            lock.unlock();
        }
        return list;
    }

    @Override
    public List<AnalysisedFile> analysisPDFXML(String depressFilePath, List<ManageEntity> allAccountList){
        List<AnalysisedFile> resList = new ArrayList<>();
        String zipFilePath = depressFilePath;
        String ext1 = FileUtil.extName(zipFilePath).toLowerCase();
        AnalysisedFile analysisedFile = new AnalysisedFile();
        if (!ext1.equalsIgnoreCase("pdf") && !ext1.equalsIgnoreCase("ofd") && !ext1.equalsIgnoreCase("xml")) {
            analysisedFile.setState("error");
            analysisedFile.setMessage("无法识别的文件格式！");
            resList.add(analysisedFile);
            return resList;
        }


        //设置基础数据
        analysisedFile.setOriginName(FileUtil.getName(zipFilePath));
        analysisedFile.setOriginPath(zipFilePath);
        String innerFileName = FileUtil.getName(zipFilePath);
        analysisedFile.setNewName(innerFileName);
        analysisedFile.setFileType(ext1);
        analysisedFile.setReceiveDate(dateTimeFmt.format(new Date()));

        //解析文件信息
        XMLInvoice invoiceInfoFromXML = new XMLInvoice();
        if (ext1.equalsIgnoreCase("xml")) {
            invoiceInfoFromXML = XMLInvoiceExtractor.getInvoiceInfoFromXML(zipFilePath);
        } else {
            invoiceInfoFromXML = InvoicePdfParser.parseInvoice(zipFilePath);
        }
        if(invoiceInfoFromXML ==null){
            analysisedFile.setState("error");
            analysisedFile.setMessage("无法从文件中解析到发票信息！");
            resList.add(analysisedFile);
            return resList;
        }
        String invoiceNumber = invoiceInfoFromXML.getNumber().trim();

        //根据发票信息获取所属账套、所属单据。
//        String buyerName = Convert.toDBC(invoiceInfoFromXML.getBuyerName() == null ? "" : invoiceInfoFromXML.getBuyerName().replaceAll(" ",""));
//        String sellerName = Convert.toDBC(invoiceInfoFromXML.getSellerName() == null ? "" : invoiceInfoFromXML.getSellerName().replaceAll(" ",""));
//
//        // 2. 执行匹配
//        MatchResult buyerResult = CompanyMatcher.findBestMatch(allAccountList, buyerName);
//        MatchResult sellerResult = CompanyMatcher.findBestMatch(allAccountList, sellerName);

// 3. 获取结果
//        if (result != null) {
//            ManageEntity matchedCompany = result.getCompany();
//            double similarity = result.getSimilarity();
//            String matchType = result.getMatchType();
//            System.out.println(matchedCompany);
//            System.out.println(similarity);
//            System.out.println(matchType);
//        }
//
//
//
//        List<ManageEntity> buyerList = allAccountList.stream().filter((ManageEntity p) -> SimilarityChecker.isSimilar(p.getName(),buyerName) ).collect(Collectors.toList());
//        List<ManageEntity> sellerList = allAccountList.stream().filter((ManageEntity p) -> SimilarityChecker.isSimilar(p.getName(),sellerName)).collect(Collectors.toList());
//        if (buyerResult !=null && sellerResult ==null) {
//            ManageEntity matchedCompany = buyerResult.getCompany();
//            analysisedFile.setAccountCode(matchedCompany.getCode());
//            analysisedFile.setAccountName(matchedCompany.getSubName());
//            analysisedFile.setMenuCode("purchaseInvoiceExcel");
//        }else if (buyerResult ==null && sellerResult !=null) {
//            ManageEntity matchedCompany = sellerResult.getCompany();
//            analysisedFile.setAccountCode(matchedCompany.getCode());
//            analysisedFile.setAccountName(matchedCompany.getSubName());
//            analysisedFile.setMenuCode("saleInvoiceExcel");
//        }else if(buyerResult !=null && sellerResult !=null){
//
//            AnalysisedFile analysisedFile1 =analysisedFile.clone();
//            //一个设置 销售，一个设置采购
//            ManageEntity sellerCompany = sellerResult.getCompany();
//            analysisedFile.setAccountCode(sellerCompany.getCode());
//            analysisedFile.setAccountName(sellerCompany.getSubName());
//            analysisedFile.setMenuCode("saleInvoiceExcel");
//
//            ManageEntity buyerCompany = buyerResult.getCompany();
//            analysisedFile1.setAccountCode(buyerCompany.getCode());
//            analysisedFile1.setAccountName(buyerCompany.getSubName());
//            analysisedFile1.setMenuCode("purchaseInvoiceExcel");
//            analysisedFile1.setInvoiceNumber(invoiceInfoFromXML.getNumber());
//            analysisedFile1.setFileContent(JSONUtil.toJsonStr(invoiceInfoFromXML));
//            analysisedFile1.setUploadType("XMLInvoice");
//
//            resList.add(analysisedFile1);
//
//        }else{
//
//        }
//        String dateStr =invoiceInfoFromXML.getDate();
//        String period = DateUtils.convertOne2AnotherDateStr(dateStr, "yyyy-MM");
//        if(!StringUtils.isEmpty(period)){
//            analysisedFile.setAccountperiod(period);
//        }



        String accountCode;
        TSaleInvoiceBackendEntity saleInvoice = tSaleInvoiceBackendDao.selectByInvoiceNumber(invoiceNumber);
        // 采购发票
        TPurchaseInvoiceBackendEntity purchaseInvoice = tPurchaseInvoiceBackendDao.selectByInvoiceNumber(invoiceNumber);
        if (saleInvoice != null && org.springframework.util.StringUtils.hasText(saleInvoice.getCheckedinvoicenumber()) && purchaseInvoice ==null) {
            // 销项发票
            accountCode = saleInvoice.getAccountcode();
            ManageEntity manageEntity = allAccountList.stream().filter((ManageEntity p) -> p.getCode().equals(accountCode)).findFirst().orElse(null);
           if(manageEntity !=null){
               // 可设置到 analysisedFile
               analysisedFile.setAccountCode(accountCode);
               analysisedFile.setAccountName(manageEntity.getSubName());
               analysisedFile.setMenuCode("saleInvoiceExcel");
               analysisedFile.setUploadType("XMLInvoice");
           }

        } else if(purchaseInvoice != null && org.springframework.util.StringUtils.hasText(purchaseInvoice.getCheckedinvoicenumber()) && saleInvoice ==null){

            accountCode = purchaseInvoice.getAccountcode();

            ManageEntity manageEntity = allAccountList.stream().filter((ManageEntity p) -> p.getCode().equals(accountCode)).findFirst().orElse(null);
            if(manageEntity !=null){
                // 可设置到 analysisedFile
                analysisedFile.setAccountCode(accountCode);
                analysisedFile.setAccountName(manageEntity.getSubName());
                analysisedFile.setMenuCode("purchaseInvoiceExcel");
                analysisedFile.setUploadType("XMLInvoice");
            }

        } else if(saleInvoice != null && org.springframework.util.StringUtils.hasText(saleInvoice.getCheckedinvoicenumber())
                && purchaseInvoice !=null && org.springframework.util.StringUtils.hasText(purchaseInvoice.getCheckedinvoicenumber())) {


            String saleAccountCode = saleInvoice.getAccountcode();
            ManageEntity saleEntity = allAccountList.stream().filter((ManageEntity p) -> p.getCode().equals(saleAccountCode)).findFirst().orElse(null);

            String purchaseAccountCode = purchaseInvoice.getAccountcode();
            ManageEntity purchaseEntity = allAccountList.stream().filter((ManageEntity p) -> p.getCode().equals(purchaseAccountCode)).findFirst().orElse(null);

            AnalysisedFile analysisedFile1 =analysisedFile.clone();
//            //一个设置 销售，一个设置采购
            analysisedFile.setAccountCode(saleAccountCode);
            analysisedFile.setAccountName(saleEntity.getSubName());
            analysisedFile.setMenuCode("saleInvoiceExcel");
            analysisedFile.setUploadType("XMLInvoice");


            analysisedFile1.setAccountCode(purchaseAccountCode);
            analysisedFile1.setAccountName(purchaseEntity.getSubName());
            analysisedFile1.setMenuCode("purchaseInvoiceExcel");
            analysisedFile1.setInvoiceNumber(invoiceInfoFromXML.getNumber());
            analysisedFile1.setFileContent(JSONUtil.toJsonStr(invoiceInfoFromXML));
            analysisedFile1.setUploadType("XMLInvoice");

            resList.add(analysisedFile1);
            }else{
                analysisedFile.setAccountCode(null);
                analysisedFile.setAccountName(null);
                analysisedFile.setMenuCode(null);
            }





        analysisedFile.setInvoiceNumber(invoiceInfoFromXML.getNumber());
        analysisedFile.setFileContent(JSONUtil.toJsonStr(invoiceInfoFromXML));
        analysisedFile.setUploadType("XMLInvoice");
        String newFilePath = "";
//        if (analysisedFile.getAccountCode() != null && analysisedFile.getMenuCode() != null) {
//            newFilePath = FileConstant.DOWNLOAD_BASEPATH + analysisedFile.getAccountCode() + File.separator + analysisedFile.getMenuCode()
//                    + File.separator + noLineDateFmt.format(new Date()) + File.separator + innerFileName;
//            //analysisedFile.setFilePath(newFilePath);
//        }
        resList.add(analysisedFile);
        return resList;

    }

    @Override
    public void updateXMLPDFPath(List<AnalysisedFile> analysisedFileList) {
        baseMapper.updateXMLPDFPath(analysisedFileList);
    }

    @Override
    public void updateBackendXMLPDFPath(List<AnalysisedFile> analysisedFileList) {
        baseMapper.updateBackendXMLPDFPath(analysisedFileList);
    }


    public AnalysisedFile getAccountInfoByInvoice(Invoice invoice, AnalysisedFile analysisedFile,List<ManageEntity> accountList, List<Map<Integer, Object>> dataList){
        if(invoice == null){
            analysisedFile.setState("error");
            analysisedFile.setMessage("无法识别的文件！");
            return analysisedFile;
        }


        String buyerName = Convert.toDBC(invoice.getBuyerName()==null?"":invoice.getBuyerName().trim().replace(" ",""));
        String sellerName = Convert.toDBC(invoice.getSellerName()==null?"":invoice.getSellerName().trim().replace(" ",""));

        List<ManageEntity> buyerList = accountList.stream().filter((ManageEntity p)->buyerName.equals(Convert.toDBC(p.getName().trim().replace(" ","")))).collect(Collectors.toList());
        List<ManageEntity> sellerList = accountList.stream().filter((ManageEntity p)->sellerName.equals(Convert.toDBC(p.getName().trim().replace(" ","")))).collect(Collectors.toList());

        if(!buyerList.isEmpty() && !sellerList.isEmpty()){
            //2024-06-07完善  如果既有采购   又有销售
            /*
              1：判断buyerList对应的账套是否已经导入了收票。判断sellerList对应的账套是否导入了开票。如果buyerList已经导入了，但是sellList并没有导入，则认为是销售开票。反之，认为是收票
              2:从历史的货物或者应税劳务名称来判断   判断出现的频率  取频率高的
              3：如果还不能判断，查看文件名称中包含开，销 字样的识别为开票信息     名称中包含收 取 识别为收票信息
              4：否则，弹框提示无法识别。
             */
            analysisedFile.setSellerCode(sellerList.get(0).getCode());
            analysisedFile.setSellerName(sellerList.get(0).getSubName());
            analysisedFile.setBuyerCode(buyerList.get(0).getCode());
            analysisedFile.setBuyerName(buyerList.get(0).getSubName());
            analysisedFile.setState("error");
            analysisedFile.setMessage("销售方和购买方同时存在于账套列表内，无法辨别所属账套！");
            return analysisedFile;

//            ManageEntity actManageBuyer = buyerList.get(0);
//            ManageEntity actManageSeller = sellerList.get(0);
//            DynamicTableNameHandler.setData(actManageBuyer.getCode());
//            List<PurchaseInvoice> buyList = purchaseInvoiceService.list(new LambdaQueryWrapper<PurchaseInvoice>().eq(PurchaseInvoice::getUploadPeriod, analysisedFile.getAccountperiod()));
//            DynamicTableNameHandler.removeData();
//
//            DynamicTableNameHandler.setData(actManageSeller.getCode());
//            List<SaleInvoice> sellList = saleInvoiceService.list(new LambdaQueryWrapper<SaleInvoice>().eq(SaleInvoice::getDate, analysisedFile.getAccountperiod()));
//            DynamicTableNameHandler.removeData();
//
//
//            String accountCodeSeller = actManageSeller.getCode();
//            String accountNameSeller = actManageSeller.getSubName() ==null?  "" : actManageSeller.getSubName().trim().replace(" ","");
//
//            String accountCodeBuyer = actManageBuyer.getCode();
//            String accountNameBuyer = actManageBuyer.getSubName() ==null?  "" : actManageBuyer.getSubName().trim().replace(" ","");
//
//            if(buyList.size()>0 && sellList.size()==0){
//                analysisedFile.setAccountCode(accountCodeSeller);
//                analysisedFile.setAccountName(actManageSeller.getSubName().trim());
//                analysisedFile.setMenuCode("saleInvoiceExcel");
//                analysisedFile.setUploadType(FileConstant.UPLOAD_TYPE_MAKEBILL);
//            }else if(buyList.size()==0 && sellList.size()>0){
//
//                analysisedFile.setAccountCode(accountCodeBuyer);
//                analysisedFile.setAccountName(accountNameBuyer);
//                analysisedFile.setMenuCode("purchasneInvoiceExcel");
//                analysisedFile.setUploadType(FileConstant.UPLOAD_TYPE_INVOICE);
//            }else{
//
//                // 3：如果还不能判断，查看文件名称中包含开，销 字样的识别为开票信息     名称中包含收 取 识别为收票信息
//                String originName = analysisedFile.getOriginName();
//                if(originName.contains("开") || originName.contains("销")){
//                    analysisedFile.setAccountCode(accountCodeSeller);
//                    analysisedFile.setAccountName(accountNameSeller);
//                    analysisedFile.setMenuCode("saleInvoiceExcel");
//                    analysisedFile.setUploadType(FileConstant.UPLOAD_TYPE_MAKEBILL);
//
//                }else if(originName.contains("取") || originName.contains("收")){
//                    analysisedFile.setAccountCode(accountCodeBuyer);
//                    analysisedFile.setAccountName(accountNameBuyer);
//                    analysisedFile.setMenuCode("purchasneInvoiceExcel");
//                    analysisedFile.setUploadType(FileConstant.UPLOAD_TYPE_INVOICE);
//                }else{
//                    analysisedFile.setState("error");
//                    analysisedFile.setMessage("没有识别到账套信息");
//                    return analysisedFile;
//                }
//            }
//2:从历史的货物或者应税劳务名称来判断   判断出现的频率  取频率高的
//                buyPd.put("taxTypeName",dataList.get(0).get(11));
//                sellPd.put("taxTypeName",dataList.get(0).get(11));
//                PageData buyFrequency = findFrequncyDataList(buyPd);
//                PageData sellFrequency = findFrequncyDataList(sellPd);
//                double bf = Double.parseDouble(buyFrequency.get("frequency").toString());
//                double sf = Double.parseDouble(sellFrequency.get("frequency").toString());
//                if(bf>sf){
//                    analysisedFile.setAccountCode(buyPd.get("code").toString());
//                    analysisedFile.setMenuCode("purchasneInvoiceExcel");
//                    analysisedFile.setUploadType(FileConstant.UPLOAD_TYPE_INVOICE);
//                }else if(bf<sf){
//                    analysisedFile.setAccountCode(sellPd.get("code").toString());
//                    analysisedFile.setMenuCode("saleInvoiceExcel");
//                    analysisedFile.setUploadType(FileConstant.UPLOAD_TYPE_MAKEBILL);
//                }else{
//
//                }
        }
        if((!buyerList.isEmpty() && buyerList.size()>1) || (sellerList.size()>0 && sellerList.size()>1)){
            analysisedFile.setState("error");
            analysisedFile.setMessage("系统内存在多个相同名称的账套，无法辨别所属账套！");
            return analysisedFile;
        }
        if(buyerList.size() ==1 && sellerList.size() ==0){

            String accountCode = buyerList.get(0).getCode();
            String accountName = buyerList.get(0).getSubName().trim().replace(" ","");
            analysisedFile.setAccountCode(accountCode);
            analysisedFile.setAccountName(accountName);
            analysisedFile.setMenuCode("purchaseInvoiceExcel");
            analysisedFile.setUploadType(FileConstant.UPLOAD_TYPE_INVOICE);

        }
        if(sellerList.size() ==1 && buyerList.size() ==0){
            String accountCode = sellerList.get(0).getCode();
            String accountName = sellerList.get(0).getSubName().trim().replace(" ","");
            analysisedFile.setAccountCode(accountCode);
            analysisedFile.setAccountName(accountName);
            analysisedFile.setMenuCode("saleInvoiceExcel");
            analysisedFile.setUploadType(FileConstant.UPLOAD_TYPE_MAKEBILL);
        }

        return analysisedFile;
    }

}

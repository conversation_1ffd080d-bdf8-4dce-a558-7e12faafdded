<template>
  <el-dialog 
     v-drag
    :modal = false
    :close-on-click-modal=false
    v-loading="loading"
    title="固定资产卡片" class="fixed-assets-dialog"
    :visible.sync="dialogVisible">
    <div class="dialog-content2" :style="{height:tableHeight+'px'}">
      <el-form :model="dataForm" :rules="dataRule" 
      v-if="type === 'create'"
      ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
      <el-form-item label="入账日期" prop="date">
        <el-date-picker
          v-model="dataForm.date"
          type="date"
          placeholder="入账日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="固定资产名称" prop="name">
        <el-input v-model="dataForm.name"></el-input>
      </el-form-item>
      <el-form-item label="固定资产编号" prop="name">
        <div style="display: flex; align-items: center;">
          <el-input v-model="dataForm.code" style="width: 200px;"></el-input>
          <span class="code-hint" style="margin-left: 10px;">存在 {{ count }} 个同品名的固定资产卡片</span>
        </div>
      </el-form-item>
      <el-form-item label="固定资产类型" prop="type">
        <el-select v-model="dataForm.type" @change="changeType">
          <el-option
            v-for="item in fixedAssetsType"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="固定资产原值" prop="oriValue">
        <el-input v-model="dataForm.oriValue" @change="changeOriValue"></el-input>
      </el-form-item>
      <el-form-item label="预计使用期数" prop="periods">
        <el-input v-model="dataForm.periods" @change="changePeriods"></el-input>
      </el-form-item>
      <el-form-item label="折旧方法" prop="depMethod">
        <el-select v-model="dataForm.depMethod">
          <el-option
            v-for="item in depMethods"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="预计残值" prop="salvage">
        <el-input v-model="dataForm.salvage"></el-input>
      </el-form-item>

      <el-form-item label="固定资产科目" prop="fixedSubject">
        <template>
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <el-input v-model="dataForm.fixedSubject" style="flex-grow: 1;"></el-input>
            <el-button size="mini" type="info" 
              class="subject-btn" v-if="type === 'create'"
              @click="showFixedSubject(dataForm,1)">选择</el-button>
          </div>
        </template>
      </el-form-item>

      <el-form-item label="累计折旧科目" prop="addFixedSubject">
        <template>
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <el-input v-model="dataForm.addFixedSubject" style="flex-grow: 1;"></el-input>
            <el-button size="mini" v-if="type === 'create'"
              type="info" class="subject-btn" @click="showFixedSubject(dataForm,2)">选择</el-button>
          </div>
        </template>
      </el-form-item>
      <el-form-item label="折旧费用科目" prop="fixedFreeSubject">
        <template>
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <el-input v-model="dataForm.fixedFreeSubject" style="flex-grow: 1;"></el-input>
            <div class="subject-btn" v-if="type === 'create'">
              <el-button size="mini" type="info" @click="showFixedSubject(dataForm,3)">选择</el-button>
            </div>
          </div>
        </template>
      </el-form-item>
      <el-form-item label="是否一次性扣除" prop="isOnce">
        <template>
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <el-checkbox v-model="dataForm.isOnce"></el-checkbox>
          </div>
        </template>
      </el-form-item>
      </el-form>
      <div v-else>
        <div :style="{height:tableHeight -40 +'px'}">
          <div v-for="(itemForm, index) in list" :key="index">
            <table class="table-with-border">
              <tr>
                <td class="td-title">入账日期</td>
                <td>{{ itemForm.date }}</td>
              </tr>
              <tr>
                <td class="td-title">固定资产名称</td>
                <td>{{ itemForm.name }}</td>
              </tr>
              <tr>
                <td class="td-title">固定资产编号</td>
                <td>{{ itemForm.code }}</td>
              </tr>
              <tr>
                <td class="td-title">固定资产类型</td>
                <td>{{ itemForm.type }}</td>
              </tr>
              <tr>
                <td class="td-title">固定资产原值</td>
                <td>{{ itemForm.oriValue }}</td>
              </tr>
              <tr>
                <td class="td-title">预计使用期数</td>
                <td>{{ itemForm.periods }}</td>
              </tr>
              <tr>
                <td class="td-title">折旧方法</td>
                <td>{{ getDepMethod(itemForm.depMethod) }}</td>
              </tr>
              <tr>
                <td class="td-title">预计残值</td>
                <td>{{ itemForm.salvage }}</td>
              </tr>
              <tr>
                <td class="td-title">固定资产科目</td>
                <td>{{ itemForm.fixedSubject }}</td>
              </tr>
              <tr>
                <td class="td-title">累计折旧科目</td>
                <td>{{ itemForm.addFixedSubject }}</td>
              </tr>
              <tr>
                <td class="td-title">折旧费用科目</td>
                <td>{{ itemForm.fixedFreeSubject }}</td>
              </tr>
              <tr>
                <td class="td-title">是否一次性扣除</td>
                <td>{{ itemForm.isOnce == 1 ? '是' : '否' }}</td>
              </tr>
            </table>
          </div>
        </div>
      </div>
    </div>
   
    <span slot="footer" class="dialog-footer" v-if="type === 'create'">
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
    <span slot="footer" class="dialog-footer" v-else-if="type === 'show'">
      <el-button @click="dialogVisible = false">关闭</el-button>
    </span>
    <subject-select-dialog
      :accountid="account.accountid"
      :visible="subjectObj.visible"
      @update:visible="subjectObj.visible = $event"
      @update-row="handleUpdateRow"
      class-name="fixed-assets-subject-dialog">
    </subject-select-dialog>
  </el-dialog>
</template>

<script>
  import SubjectSelectDialog from './subject-select-dialog.vue'
  import { randomNumber } from '@/utils'
  import { getDialogHeight } from '@/utils/tableUtils.js'
  export default {  
    props: {
      visible: {
        type: Boolean,
        default: false
      },
      data: {
        type: Object,
        default: () => {}
      },
      account: {
        type: Object,
        default: () => {}
      },
      type: {
        type: String,
        default: ''
      }
    },
    components: {
      SubjectSelectDialog
    },
    data () {
      return {
        dialogVisible: false,
        loading: false,
        dataForm: {
          sourceid: '',
          date: '',
          name: '',
          code: '',
          type: '',
          quantity: '',
          oriValue: '',
          periods: '',
          depMethod: '',
          salvage: '',
          fixedSubject: '',
          addFixedSubject: '',
          fixedFreeSubject: '',
          sourceInvoice: '',
          sourcePurchase: '',
          accountid: '',
          fixedAmount: '',
          isOnce: false
        },
        list:[],
        dataRule: {
          date: [{ required: true, message: '入账日期不能为空', trigger: 'blur' }],
          name: [{ required: true, message: '固定资产名称不能为空', trigger: 'blur' }],
          code: [{ required: true, message: '固定资产编号不能为空', trigger: 'blur' }],
          type: [{ required: true, message: '固定资产类型不能为空', trigger: 'blur' }],
          oriValue: [
            { required: true, message: '请输入正数或正小数', trigger: 'blur' },
            { validator: this.validatePositiveNumber, trigger: 'blur' }
          ],
          periods: [{ required: true, message: '预计使用期数不能为空', trigger: 'blur' }],
          depMethod: [{ required: true, message: '折旧方法不能为空', trigger: 'blur' }],
          salvage: [
            { required: true, message: '请输入正数或正小数', trigger: 'blur' },
            { validator: this.validatePositiveNumber, trigger: 'blur' }
          ],
          fixedSubject: [{ required: true, message: '固定资产科目不能为空', trigger: 'blur' }],
          addFixedSubject: [{ required: true, message: '累计折旧科目不能为空', trigger: 'blur' }],
          fixedFreeSubject: [{ required: true, message: '折旧费用科目不能为空', trigger: 'blur' }]
        },
        fixedAssetsType: [
          {value: '房屋及建筑物', label: '房屋及建筑物'},
          {value: '机械设备', label: '机械设备'},
          {value: '工具、器具、家具', label: '工具、器具、家具'},
          {value: '车辆及运输工具', label: '车辆及运输工具'},
          {value: '电子设备', label: '电子设备'},
          {value: '其他', label: '其他'}
        ],
        depMethods: [
          {value: '0', label: '平均年限法'}
        ],
        subjectObj: {
          type: '',
          visible: false
        },
        residualVal: 100,
        count: 0,
        tableHeight:0
      }
    },
    methods: {
      getHeight(){
        this.tableHeight = getDialogHeight('fixed-assets-dialog .el-dialog')
      },
      // 初始化
      init () {
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.dataForm.isOnce = this.dataForm.isOnce ? 1 : 0
            this.$http({
              url: this.$http.adornUrl('/ans/ansaccountindex/fixedAssets/save'),
              method: 'post',
              data: this.dataForm
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.dialogVisible = false
                    this.$emit('refresh-data', {})
                    this.$nextTick(() => {
                      this.dataForm = []
                    })
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      },
      showFixedSubject (row, type) {
        this.subjectObj.type = type
        this.subjectObj.visible = true
      },
      changeType (val) {
        switch (val) {
          case '房屋及建筑物':
            this.dataForm.typeid = '1'
            break
          case '机械设备':
            this.dataForm.typeid = '2'
            break
          case '工具、器具、家具':
            this.dataForm.typeid = '3'
            break
          case '车辆及运输工具':
            this.dataForm.typeid = '4'
            break
          case '电子设备':
            this.dataForm.typeid = '5'
            break
          case '其他':
            this.dataForm.typeid = '6'
            break
        }
        this.initType(this.dataForm)
      },
      async initType (form, code) {
        let data = {}
        if (code) {
          data = {
            taxCode: code,
            accountid: this.account.accountid,
            count: form.quantity
          }
        } else {
          data = {
            id: this.dataForm.typeid,
            accountid: this.account.accountid,
            count: form.quantity
          }
        }
        await this.$http({
          url: this.$http.adornUrl('/ans/ansaccountindex/getFixedAssets'),
          method: 'post',
          data: data
        }).then(({data}) => {
          if (data && data.code === 0) {
            debugger
            let result = data.result
            if (result) {
              this.dataForm.accountid = this.account.accountid
              this.dataForm.typeid = result.id
              this.dataForm.type = result.name
              this.dataForm.periods = result.deperMonth
              this.residualVal = result.residualVal
              this.dataForm.salvage = (parseFloat(form.oriValue) *
               parseFloat(result.residualVal) / 100).toFixed(2)
              this.count = result.count
              this.dataForm.code = `${this.dataForm.typeid}${result.code}`
            }
          }
          this.loading = false
        })
      },
      handleUpdateRow (row) {
        if (this.subjectObj.type === 1) {
          this.dataForm.fixedSubject = row.name
          this.dataForm.fixedSubjectId = row.id
        }else if (this.subjectObj.type === 2) {
          this.dataForm.addFixedSubject = row.name
          this.dataForm.addFixedSubjectId = row.id
        }else if (this.subjectObj.type === 3) {
          this.dataForm.fixedFreeSubject = row.name
          this.dataForm.fixedFreeSubjectId = row.id
        }
      },
      initData (val) {
        if (this.type === 'create') {
          this.dataForm.date = val.useDate
          this.dataForm.name = val.taxTypeName
          let count = val.fixedQuantity != null ? val.fixedQuantity : val.quantity
          let amount = 0

          if (count == 0) {
            count = 1
          }
          
          if (this.account.nature === '一般纳税人') {
            amount = val.sumAmount
            this.dataForm.oriValue = (parseFloat(val.sumAmount) / parseFloat(count)).toFixed(2)
          }else if (this.account.nature === '小规模纳税人') {
            amount = val.sumTotalAmount
            this.dataForm.oriValue = (parseFloat(val.sumTotalAmount) / parseFloat(count)).toFixed(2)
          }
          this.dataForm.quantity = count
          this.dataForm.fixedAmount = amount
          this.dataForm.depMethod = '0'
          this.dataForm.sourceid = val.id
          this.initType(this.dataForm,val.taxTypeCode)
          this.dataForm.fixedSubject = '固定资产'
          this.dataForm.addFixedSubject = '累计折旧'
          this.dataForm.fixedFreeSubject = '管理费用-累计折旧'
          this.dataForm.sourceInvoice = val.invoiceNumber == null ? '' : val.invoiceNumber
          this.dataForm.sourcePurchase = val.sellerName == null ? '' : val.sellerName
        }else {
          this.getFixedAssetCard()
        }
      },
      async getFixedAssetCard () {
        await this.$http({
          url: this.$http.adornUrl('/ans/ansaccountindex/getFixedCard'),
          method: 'post',
          data: {
            accountid: this.account.accountid,
            sourceid: this.data.id
          }
        }).then(({data}) => {
          if (data && data.code === 0) {
            if (data.result) {
              this.list = data.result
              this.dataForm.depMethod = String(this.dataForm.depMethod)
            }else{
              this.$message.error('获取数据失败')
            }
          }
        })
      },
      validatePositiveNumber(rule, value, callback) {
        const reg = /^(0|[1-9][0-9]*)(\.[0-9]+)?$/;
        if (!reg.test(value)) {
          callback(new Error('请输入正数或正小数'));
        } else {
          callback();
        }
      },
      changeOriValue (val) {
        this.dataForm.salvage = (parseFloat(val) *
               parseFloat(this.residualVal) / 100).toFixed(2)
      },
      changePeriods (val) {
        this.dataForm.periods = val.replace(/[^\d]/g, '');
      },
      getDepMethod (val) {
        if (String(val) === '0') {
          return '平均年限法'
        }
      },
      show(){
        this.dialogVisible = true
        this.initData(this.data)
        this.$nextTick(() => {
          this.getHeight();
        })
      }
    },
    created () {
      this.dialogVisible = this.visible
    },
    watch: {
      visible (val) {
        this.dialogVisible = val
        if (val) {
          setTimeout(()=>{
            this.getHeight();
          },100)
          this.$nextTick(() => {
            this.initData(this.data)
          })
        }
      },
      dialogVisible (val) {
        this.$emit('update:visible', val)
      },
      data (val) {
        // date: '',name: '',code: '',type: '',oriValue: '',periods: '',
        // depMethod: '',salvage: '',fixedSubject: '',addFixedSubject: '',fixedFreeSubject: ''
        this.initData(val)
      } 
    }
  }
</script>
<style scoped>
  .dialog-content2{
    overflow-y: auto;
  }
  .subject-btn {
    margin-left: 10px;
  }

  .table-with-border {
    border-collapse: collapse; /* 合并边框 */
    width: 100%;
    margin-top: 20px;
  }

  .table-with-border th, .table-with-border td {
    border: 1px solid #000; /* 添加边框 */
    padding: 8px;
    text-align: left;
  }

  .td-title {
    font-weight: bold; /* 加粗字体 */
    color: #3333339c; /* 设置字体颜色 */
  }
  .code-hint{
    color: brown;
    font-weight: bold;
  }
</style>


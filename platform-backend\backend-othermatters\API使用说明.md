# 其他事项模块 API 使用说明

## 概述

其他事项模块支持两种类型的业务：
1. **分红** (mattersType = "1") - 需要保存明细表数据
2. **待摊费用分摊** (mattersType = "2") - 只需要主表数据

## 主要接口

### 1. 分页查询列表
```
GET /othermatters/tothermatters/list
```
参数：
- accountPeriod: 会计期间
- mattersType: 事项类型 (1:分红 2:待摊费用分摊)
- status: 状态
- page: 页码
- limit: 每页数量

### 2. 查询详情
```
GET /othermatters/tothermatters/info/{id}
```
返回：
- 如果是分红类型，会同时返回明细数据

### 3. 保存（推荐使用DTO方式）
```
POST /othermatters/tothermatters/saveWithDTO
```
请求体示例：
```json
{
  "accountCode": "001",
  "otherMatters": {
    "accountPeriod": "2024-01",
    "mattersType": "1",
    "mattersStart": "2024-01-01",
    "mattersEnd": "2024-01-31",
    "status": "0",
    "createBy": "admin"
  },
  "detailList": [
    {
      "empId": 1,
      "empName": "张三",
      "shareMoney": 10000.00,
      "taxMoney": 2000.00
    },
    {
      "empId": 2,
      "empName": "李四",
      "shareMoney": 15000.00,
      "taxMoney": 3000.00
    }
  ]
}
```

### 4. 更新（推荐使用DTO方式）
```
POST /othermatters/tothermatters/updateWithDTO
```
请求体格式同保存接口

### 5. 删除
```
POST /othermatters/tothermatters/delete?accountCode=001
```
请求体：
```json
[1, 2, 3]
```

### 6. 根据会计期间查询
```
GET /othermatters/tothermatters/getByAccountPeriod/{accountPeriod}
```

### 7. 根据会计期间和类型查询
```
GET /othermatters/tothermatters/getByAccountPeriodAndType/{accountPeriod}/{mattersType}
```

### 8. 查询分红明细
```
GET /othermatters/tothermatters/detail/getByAccountPeriod/{accountPeriod}
```

## 数据结构说明

### 主表字段 (t_other_matters)
- id: 主键ID
- accountPeriod: 会计期间
- mattersType: 事项类型 (1:分红 2:待摊费用分摊)
- mattersStart: 开始时间
- mattersEnd: 结束时间
- prepaidSubjectId: 预付科目ID
- prepaidSubjectName: 预付科目名称
- prepaidMoney: 分摊总金额
- periodMoney: 每期分摊金额
- costSubjectId: 费用科目ID
- costSubjectName: 费用科目名称
- status: 状态
- createBy: 创建人
- createTime: 创建时间
- updateBy: 修改人
- updateTime: 修改时间

### 明细表字段 (t_other_matters_detail)
- id: 主键ID
- accountPeriod: 会计期间
- empId: 职员ID
- empName: 职员名称
- shareMoney: 分红金额
- taxMoney: 应交个税

## 业务逻辑说明

### 分红业务 (mattersType = "1")
1. 保存主表数据到 t_other_matters
2. 保存明细数据到 t_other_matters_detail
3. 删除时会同时删除相关明细数据

### 待摊费用分摊业务 (mattersType = "2")
1. 只保存主表数据到 t_other_matters
2. 主要字段：prepaidSubjectId, prepaidSubjectName, prepaidMoney, periodMoney, costSubjectId, costSubjectName

## 注意事项

1. 所有接口都需要传递 accountCode 参数用于多租户数据隔离
2. 分红类型的数据删除会级联删除相关明细
3. 推荐使用 DTO 方式的接口，数据结构更清晰
4. 会计期间格式建议使用 "YYYY-MM" 格式
5. 金额字段使用 BigDecimal 类型，精度为 18,2

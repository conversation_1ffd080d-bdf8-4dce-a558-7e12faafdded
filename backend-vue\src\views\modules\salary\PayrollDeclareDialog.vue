<template>
  <el-dialog
    v-drag
    :modal = false
    :close-on-click-modal=false
    :visible.sync="visible"
    width="90%"
    @close="$emit('close')"
    @closed="onDialogClosed"
    :custom-class="className"
  >
  <div slot="title">
      <div class="title_label">申报数据</div>
  </div> 
    <div class="dialog-content" :style="{height:tableHeight+'px'}">
      <el-table ref="declareTable" :data="payrollData" border style="width: 100%" height="100%" >
        <el-table-column prop="empdocid" label="工号" width="100" />
        <el-table-column prop="empdoc_name" label="*姓名" width="100" />
        <el-table-column prop="cardType" label="*证件类型" width="120"/>
        <el-table-column prop="idcard" label="*证件号码" width="170" />
        <el-table-column prop="本期收入" label="本期收入" width="120">
          <template slot-scope="scope">
            {{ scope.row && (scope.row.income || scope.row['本期收入']) ? (scope.row.income || scope.row['本期收入']) : '' }}
          </template>
        </el-table-column>
        <el-table-column prop="本期免税收入" label="本期免税收入" width="120" />
        <el-table-column prop="基本养老保险费" label="基本养老保险费" width="120" />
        <el-table-column prop="基本医疗保险费" label="基本医疗保险费" width="120" />
        <el-table-column prop="失业保险费" label="失业保险费" width="120" />
        <el-table-column prop="住房公积金" label="住房公积金" width="120" />
        <el-table-column prop="累计子女教育" label="累计子女教育" width="120" />
        <el-table-column prop="累计继续教育" label="累计继续教育" width="120" />
        <el-table-column prop="累计住房贷款利息" label="累计住房贷款利息" width="150" />
        <el-table-column prop="累计住房租金" label="累计住房租金" width="120" />
        <el-table-column prop="累计赡养老人" label="累计赡养老人" width="120" />
        <el-table-column prop="累计3岁以下婴幼儿照护" label="累计3岁以下婴幼儿照护" width="120" />
        <el-table-column prop="累计个人养老金" label="累计个人养老金" width="120" />
        <el-table-column prop="企业(职业)年金" label="企业(职业)年金" width="120" />
        <el-table-column prop="商业健康保险" label="商业健康保险" width="120" />
        <el-table-column prop="税延养老保险" label="税延养老保险" width="120" />
        <el-table-column prop="其他" label="其他" width="120" />
        <el-table-column prop="准予扣除的捐赠额" label="准予扣除的捐赠额" width="150" />
        <el-table-column prop="减免税额" label="减免税额" width="120" />
        <el-table-column prop="备注" label="备注" width="120" />
      </el-table>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="exportExcel" type="primary">导出</el-button>
      <el-button @click="$emit('close')">关闭</el-button>
    </span>
  </el-dialog>
</template>

<script>
import * as XLSX from 'xlsx';
// 引入对话框内容高度计算mixin
import dialogContentCalculate from '@/mixins/dialogContentCalculate';

export default {
  name: 'PayrollDeclareDialog',
  mixins: [dialogContentCalculate],
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    payrollData: {
      type: Array,
      default: () => []
    },
    className: {
        type: String,
        default: ''
      },
  },
  data() {
    return {
     
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.$nextTick(() => {
          this.openDialog();
        });
      }
    }
  },
  methods: {
    openDialog() {
      // 使用dialogContentCalculate mixin计算表格高度
      this.$nextTick(() => {
        this.initTableHeight(this.className, {
          delay: 300,
          setupListener: true,
          callback: (height) => {
            if (height && height > 0) {
              this.tableHeight = height;
            }
          }
        });
      });
    },
    onDialogClosed() {
      // 对话框关闭时的处理
  
      // 清理资源或重置状态
      this.removeResizeListener && this.removeResizeListener();
    },
    exportExcel() {
      // 获取表头顺序
      const columns = [
        { prop: 'empdocid', label: '工号' },
        { prop: 'empdoc_name', label: '*姓名' },
        { prop: 'cardType', label: '*证件类型' },
        { prop: 'idcard', label: '*证件号码' },
        { prop: '本期收入', label: '本期收入' },
        { prop: '本期免税收入', label: '本期免税收入' },
        { prop: '基本养老保险费', label: '基本养老保险费' },
        { prop: '基本医疗保险费', label: '基本医疗保险费' },
        { prop: '失业保险费', label: '失业保险费' },
        { prop: '住房公积金', label: '住房公积金' },
        { prop: '累计子女教育', label: '累计子女教育' },
        { prop: '累计继续教育', label: '累计继续教育' },
        { prop: '累计住房贷款利息', label: '累计住房贷款利息' },
        { prop: '累计住房租金', label: '累计住房租金' },
        { prop: '累计赡养老人', label: '累计赡养老人' },
         { prop: '累计3岁以下婴幼儿照护', label: '累计3岁以下婴幼儿照护' },
          { prop: '累计个人养老金', label: '累计个人养老金' },
        { prop: '企业(职业)年金', label: '企业(职业)年金' },
        { prop: '商业健康保险', label: '商业健康保险' },
        { prop: '税延养老保险', label: '税延养老保险' },
        { prop: '其他', label: '其他' },
        { prop: '准予扣除的捐赠额', label: '准予扣除的捐赠额' },
        { prop: '减免税额', label: '减免税额' },
        { prop: '备注', label: '备注' }
      ];
      // 组装导出数据
      const exportData = [];
      // 表头
      exportData.push(columns.map(col => col.label));
      // 数据
      this.payrollData.forEach(row => {
        exportData.push(columns.map(col => row[col.prop] || ''));
      });
      // 导出
      const ws = XLSX.utils.aoa_to_sheet(exportData);
      // 设置列宽
      const wideCols = ['证件号码','本期收入', '基本养老保险费', '基本医疗保险费', '失业保险费', '住房公积金'];
      ws['!cols'] = columns.map(col => {
        if (wideCols.includes(col.label)) {
          return { wch: Math.round(12 * 1.2) };
        } else {
          return { wch: 12 };
        }
      });
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, '申报数据');
      XLSX.writeFile(wb, '申报数据.xlsx');
    }
  }
}
</script>

<style scoped>

</style> 
<template>
    <el-dialog
      v-drag
      :modal = false
      :close-on-click-modal=false
      width="80%"
      custom-class="salary-module-insurance-preview"
      :before-close="handleClose"
      :visible.sync="visible"
      >
      <div slot="title">
        <div class="title_label">保险数据</div>
    </div>
   
    <div class="dialog-content" :style="{height:tableHeight+'px'}">
      <el-tabs v-model="activeName" type="card" @tab-click="handleClick" class="full-height-tabs">
        <el-tab-pane label="社保单" name="first">
          <div class="table-container">
            <div class="top-bar">
            <div class="action-buttons">
              <el-tooltip :disabled="ansStatus != 0" :content="hintMsg" placement="top">
                <el-button 
                  :disabled="ansStatus == 0"
                  type="primary" @click="addEmp('insurance')">新增员工</el-button>
              </el-tooltip>
              <el-tooltip :disabled="ansStatus != 0" :content="hintMsg" placement="top">
                <el-button 
                 :disabled="ansStatus == 0"
                  type="info" @click="showDoc('insurance')">参数</el-button>
              </el-tooltip>
              <el-tooltip :disabled="ansStatus != 0" :content="hintMsg" placement="top">
                <el-button 
                  :disabled="ansStatus == 0"
                  type="info" @click="adjustBase('insurance')">调基</el-button>
              </el-tooltip> 
              
              <el-tooltip :disabled="ansStatus != 0" :content="hintMsg" placement="top">
                <el-button 
                 :disabled="ansStatus == 0"
                  class="btn-white" @click="frontGenerate('insurance')">重置数据</el-button>
              </el-tooltip>  
              
              <el-button class="btn-white" @click="exportInsuraceExcel">导出</el-button>
              <div v-if="!isInsuranceConfirmed" class="confirm-status" @click="confirmData('insurance')">
                <i class="el-icon-time"></i>
                <span>待确认</span>
              </div>
            </div>
            <div class="total-amount">
              本期需缴纳 <span class="amount-value">{{ formatAmount(calculateTotalAmount()) }}</span> 元
            </div>
          </div>
          

          <el-table
            v-loading="loading"
            height="100%"
            show-summary
            :summary-method="getSummaries"
            :data="insuranceData"
            row-key="id"
            border
            id="insurance-table"
            default-expand-all
            @selection-change="saleSelectionChangeHandle"
            highlight-current-row
            :row-style="{height:'30px'}"
            :cell-style="{padding:'5px'}"
             @cell-mouse-enter="insuranceHandleMouseEnter"
            @cell-mouse-leave="insuranceHandleMouseLeave"
          
          >
          
            <el-table-column type="index" label="序号" align="center" width="50"> </el-table-column>
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column prop="empdoc_name" header-align="center" align="center" width="100" label="姓名"></el-table-column>
            <el-table-column prop="dept_name" header-align="center" align="center" width="100" label="部门"></el-table-column>
            <el-table-column prop="basemoney" header-align="center" align="right" width="100" label="申报工资">
              <template slot-scope="scope">
                {{ formatNumber(scope.row.basemoney) }}
              </template>
            </el-table-column>
            
            <el-table-column prop="old_basemoney" header-align="center" align="right" width="100" label="养老基数">
              <template slot-scope="scope">
                {{ formatNumber(scope.row.old_basemoney) }}
              </template>
            </el-table-column>
            <el-table-column header-align="center" label="单位部分">
              <el-table-column
                v-for="(column, index) in insuranceComppartItems"
                :key="index"
                :prop="column.insuranceitemscode"
                :label="column.insuranceitemsname"
                header-align="center" align="right" width="110">
                <template slot-scope="scope">
                  <el-input 
                    v-if="scope.row.isEditing === true"
                    v-model="scope.row[column.insuranceitemscode]" 
                    placeholder="请输入内容"
                    v-number
                    class="number-input"
                    @blur="formatNumberOnBlurInsurance(scope.row, column.insuranceitemscode)"></el-input>
                  <div v-else>{{ formatNumber(scope.row[column.insuranceitemscode]) }}</div>
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column prop="compparttotal" header-align="center" align="center" width="110" label="单位合计">
              <template slot-scope="scope">
                {{ formatNumber(scope.row.compparttotal) }}
              </template>
            </el-table-column>
            <el-table-column header-align="center" label="个人部分">
              <el-table-column
                v-for="(column, index) in insurancePersonpartItems"
                :key="index"
                :prop="column.insuranceitemscode"
                :label="column.insuranceitemsname"
                header-align="center" align="right" width="110">
                <template slot-scope="scope">
                  <div class="item">
                    <el-input 
                      v-if="scope.row.isEditing === true"
                      v-model="scope.row[column.insuranceitemscode]" 
                      placeholder="请输入内容"
                      v-number
                      class="number-input"
                      @blur="formatNumberOnBlurInsurance(scope.row, column.insuranceitemscode)"></el-input>
                    <div v-else>{{scope.row[column.insuranceitemscode]}}</div>
                  </div>
                </template>
              </el-table-column>
            </el-table-column>

            <el-table-column prop="personparttotal" header-align="center" align="center" width="100" label="个人合计">
              <template slot-scope="scope">
                {{ formatNumber(scope.row.personparttotal) }}
              </template>
            </el-table-column>
            
            
            <el-table-column prop="totalparttotal" header-align="center" align="center" width="100" label="合计">
              <template slot-scope="scope">
                {{ formatNumber(scope.row.totalparttotal) }}
              </template>
            </el-table-column>
            <el-table-column header-align="center" align="center" width="120" label="操作" :fixed="getTableScrollStatus('insurance-table') ? 'right' : false">
              <template slot-scope="scope">
                <!-- 编辑模式下显示 -->
                <template v-if="scope.row.isEditing === true">
                  <el-button type="text" class="status-link" @click="saveRowEdit(scope.row, 'insurance')">确定</el-button>
                  <el-button type="text" class="status-link" @click="cancelRowEdit(scope.row)">取消</el-button>
                </template>
                
                <!-- 非编辑模式下显示 -->
                <template v-else>
                  <el-tooltip :disabled="ansStatus != 0" :content="hintMsg" placement="top">
                    <el-button 
                      :disabled="ansStatus == 0"
                      type="text" class="status-link" @click="editRow(scope.row)">修改</el-button>
                  </el-tooltip>
                  <el-tooltip :disabled="ansStatus != 0" :content="hintMsg" placement="top">
                    <el-button 
                      :disabled="ansStatus == 0"
                      type="text" class="status-link" @click="handleDelete(scope.row,'insurance')">删除</el-button>
                  </el-tooltip>
                </template>
              </template>
            </el-table-column>
          </el-table>
       
          </div>
       
          
        
        </el-tab-pane>

        <el-tab-pane label="公积金单" name="second">
          <div class="table-container">
            <div class="top-bar">
            <div class="action-buttons">
              <el-tooltip :disabled="ansStatus != 0" :content="hintMsg" placement="top">
                <el-button 
                  :disabled="ansStatus == 0"
                  type="primary" @click="addEmp('housefund')">新增员工</el-button>
              </el-tooltip>
              <el-tooltip :disabled="ansStatus != 0" :content="hintMsg" placement="top">
                <el-button 
                  :disabled="ansStatus == 0"
                  type="info" @click="showDoc('housefund')">参数</el-button>
              </el-tooltip>
              <el-tooltip :disabled="ansStatus != 0" :content="hintMsg" placement="top">
                <el-button 
                  :disabled="ansStatus == 0"
                  type="info" @click="adjustBase('housefund')">调基</el-button>
              </el-tooltip>
              <el-tooltip :disabled="ansStatus != 0" :content="hintMsg" placement="top">
                <el-button 
                  :disabled="ansStatus == 0"
                  class="btn-white" @click="frontGenerate('housefund')">重置数据</el-button>
              </el-tooltip>
              <el-button class="btn-white" @click="exportHousefundExcel">导出</el-button>
              <div v-if="!isHousefundConfirmed" class="confirm-status" @click="confirmData('housefund')">
                <i class="el-icon-time"></i>
                <span>待确认</span>
              </div>
            </div>
            <div class="total-amount">
              本期需缴纳 <span class="amount-value">{{ formatAmount(calculateHousefundTotalAmount() || 0) }}</span> 元
            </div>
          </div>
          
          <el-table
            v-loading="loading"
            height="100%"
            show-summary
            :summary-method="getSummaries"
            :data="housefundData"
            row-key="id"
            border
            id="housefund-table"
            default-expand-all
            @selection-change="housefundSelectionChangeHandle"
            highlight-current-row
            :row-style="{height:'30px'}"
            :cell-style="{padding:'5px'}"
            @cell-mouse-enter="housefundHandleMouseEnter"
            @cell-mouse-leave="housefundHandleMouseLeave"
          >
          
            <el-table-column type="index" label="序号" align="center" width="50"> </el-table-column>
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column prop="empdoc_name" header-align="center" align="center" width="100" label="姓名"></el-table-column>
            <el-table-column prop="dept_name" header-align="center" align="center" width="100" label="部门"></el-table-column>
            <el-table-column prop="basemoney" header-align="center" 
            align="right" width="120" label="公积金基数">
            <template slot-scope="scope">
              {{ formatNumber(scope.row.basemoney) }}
            </template>
          </el-table-column>
            <el-table-column header-align="center" label="单位部分">
              <el-table-column
                v-for="(column, index) in housefundComppartItems"
                :key="index"
                :prop="column.insuranceitemscode"
                :label="column.insuranceitemsname"
                header-align="center" align="right" width="120">
                <template slot-scope="scope">
                  <div class="item">
                    <el-input 
                      v-if="scope.row.isEditing === true"
                      v-model="scope.row[column.insuranceitemscode]" 
                      placeholder="请输入内容"
                      v-number
                      class="number-input"
                     
                      @blur="formatNumberOnBlurHousefund(scope.row, column.insuranceitemscode)"></el-input>
                    <div v-else>{{formatNumber(scope.row[column.insuranceitemscode])}}</div>
                  </div>
                </template>
              </el-table-column>
            </el-table-column>
            <!-- <el-table-column prop="compparttotal" header-align="center" align="center" width="100" label="单位合计"></el-table-column> -->
            <el-table-column header-align="center" label="个人部分">
              <el-table-column
                v-for="(column, index) in housefundPersonpartItems"
                :key="index"
                :prop="column.insuranceitemscode"
                :label="column.insuranceitemsname"
                header-align="center" align="right" width="120">
                <template slot-scope="scope">
                  <div class="item">
                    <el-input 
                      v-if="scope.row.isEditing === true"
                      v-model="scope.row[column.insuranceitemscode]" 
                      placeholder="请输入内容"
                      v-number
                      class="number-input"
                    
                      @blur="formatNumberOnBlurHousefund(scope.row, column.insuranceitemscode)"></el-input>
                    <div v-else>{{formatNumber(scope.row[column.insuranceitemscode])}}</div>
                  </div>
                </template>
              </el-table-column>
            </el-table-column>
            <!-- <el-table-column prop="personparttotal" header-align="center" align="center" width="100" label="个人合计"></el-table-column> -->
            
            <el-table-column prop="totalparttotal" header-align="center" align="center" width="120" label="合计">
              <template slot-scope="scope">
                {{ formatNumber(scope.row.totalparttotal) }}
              </template>
            </el-table-column>

            
            <el-table-column header-align="center" align="center" width="150" label="操作" :fixed="getTableScrollStatus('housefund-table') ? 'right' : false">
             <template slot-scope="scope">
                <!-- 编辑模式下显示 -->
                <template v-if="scope.row.isEditing === true">
                  <el-button type="text" class="status-link" @click="saveRowEdit(scope.row, 'housefund')">确定</el-button>
                  <el-button type="text" class="status-link" @click="cancelRowEdit(scope.row)">取消</el-button>
                </template>
                
                <!-- 非编辑模式下显示 -->
                <template v-else>
                  <el-tooltip :disabled="ansStatus != 0" :content="hintMsg" placement="top">
                    <el-button 
                      :disabled="ansStatus == 0"
                      type="text" class="status-link" @click="editRow(scope.row)">修改</el-button>
                  </el-tooltip>
                  <el-tooltip :disabled="ansStatus != 0" :content="hintMsg" placement="top">
                    <el-button 
                      :disabled="ansStatus == 0"
                      type="text" class="status-link" @click="handleDelete(scope.row,'housefund')">删除</el-button>
                  </el-tooltip>
                </template>
              </template>
            </el-table-column>
          </el-table>
          </div>
        </el-tab-pane>



        <el-tab-pane label="外包保险" name="fourth">
          <div class="table-container">
            <div class="top-bar">
            <div class="action-buttons">
              <el-tooltip :disabled="ansStatus != 0" :content="hintMsg" placement="top">
                <el-button 
                  :disabled="ansStatus == 0"
                  type="primary" @click="addEmp('outsource')">新增员工</el-button>
              </el-tooltip>
              
              <!-- <el-button type="info" @click="showDoc('outsource')">参数</el-button>
              <el-button type="info" @click="adjustBase('outsource')">调基</el-button> -->
              <el-button type="info" @click="showTemp()">查看模版</el-button>
              <el-tooltip :disabled="ansStatus != 0" :content="hintMsg" placement="top">
                <el-button 
                :disabled="ansStatus == 0" class="btn-white" @click="uploadDetail2()">导入</el-button>
              </el-tooltip>
              
              <el-button class="btn-white" @click="exportOutsourceExcel">导出</el-button>
              <el-tooltip :disabled="ansStatus != 0" :content="hintMsg" placement="top">
                <div 
                  v-if="!isOutsourceConfirmed" 
                  :class="ansStatus == 0 ? 'confirm-status-disabled' : 'confirm-status'"
                  @click="ansStatus == 0 ? null : confirmData('outsource')">
                  <i class="el-icon-time"></i>
                  <span>待确认</span>
                </div>
              </el-tooltip>
            </div>
            <div class="total-amount">
              本期需缴纳 <span class="amount-value">{{ formatAmount(calculateOutsourceTotalAmount() || 0) }}</span> 元
          </div>
          </div>
          
          <el-table
            v-loading="loading"
            height="100%"
            show-summary
            :summary-method="getSummaries"
            :data="outsourceData"
            row-key="id"
            border
            id="outsource-table"
            default-expand-all
            @selection-change="saleSelectionChangeHandle"
            highlight-current-row
            :row-style="{height:'30px'}"
            :cell-style="{padding:'5px'}"
          >
            <el-table-column type="index" label="序号" align="center" width="50"> </el-table-column>
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column prop="empdoc_name" header-align="center" align="center" width="100" label="姓名"></el-table-column>
            <el-table-column prop="dept_name" header-align="center" align="center" width="100" label="部门"></el-table-column>
            <el-table-column prop="insuranceBasemoney" header-align="center" align="right" width="100" label="参保基数">
              <template slot-scope="scope">
                <div class="item">
                  <el-input 
                    v-if="scope.row.isEditing === true"
                    v-model="scope.row['insuranceBasemoney']" 
                    placeholder="请输入内容"
                    v-number
                    class="number-input"
                  
                    @blur="formatNumberOnBlurOutsource(scope.row, 'insuranceBasemoney')"></el-input>
                  <div v-else class="item__txt">{{formatNumber(scope.row['insuranceBasemoney'])}}</div>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column header-align="center" label="单位部分">
               <el-table-column
                v-for="(column, index) in outsourceComppartItems"
                :key="index"
                :prop="column.code"
                :label="column.name"
                header-align="center" align="right" width="100">
                <template slot-scope="scope">
                  <div class="item">
                    <el-input 
                      v-if="scope.row.isEditing === true"
                      v-model="scope.row[column.code]" 
                      placeholder="请输入内容"
                      v-number
                      class="number-input"
                      @blur="formatNumberOnBlurOutsource(scope.row, column.code)"></el-input>
                    <div v-else class="item__txt">{{formatNumber(scope.row[column.code])}}</div>
                  </div>
                </template>
              </el-table-column> 
            </el-table-column>
            <el-table-column prop="compPartTotal" header-align="center" align="center" width="100" label="单位小计"></el-table-column>
            <el-table-column header-align="center" label="个人部分">
               <el-table-column
                v-for="(column, index) in outsourcePersonpartItems"
                :key="index"
                :prop="column.code"
                :label="column.name"
                header-align="center" align="right" width="100">
                <template slot-scope="scope">
                  <div class="item">
                    <el-input 
                      v-if="scope.row.isEditing === true"
                      v-model="scope.row[column.code]" 
                      placeholder="请输入内容"
                      v-number
                      class="number-input"
                    
                      @blur="formatNumberOnBlurOutsource(scope.row, column.code)"></el-input>
                    <div v-else class="item__txt">{{formatNumber(scope.row[column.code])}}</div>
                  </div>
                </template>
              </el-table-column> 
            </el-table-column>
            <el-table-column prop="personPartTotal" header-align="center" align="center" width="100" label="个人小计"></el-table-column>
            
            <el-table-column prop="insurancePartTotal" header-align="center" align="center" width="100" label="社保合计"></el-table-column>

            <el-table-column prop="housefundBasemoney" header-align="center" align="right" width="100" label="公积金基数">
              <template slot-scope="scope">
                <div class="item">
                  <el-input 
                    v-if="scope.row.isEditing === true"
                    v-model="scope.row['housefundBasemoney']" 
                    placeholder="请输入内容"
                    v-number
                    class="number-input"
                  
                    @blur="formatNumberOnBlurOutsource(scope.row, 'housefundBasemoney')"></el-input>
                  <div v-else class="item__txt">{{formatNumber(scope.row['housefundBasemoney'])}}</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="housefund-comppart" header-align="center" align="right" width="100" label="公积金公司">
              <template slot-scope="scope">
                <div class="item">
                  <el-input 
                    v-if="scope.row.isEditing === true"
                    v-model="scope.row['housefund-comppart']" 
                    placeholder="请输入内容"
                    v-number
                    class="number-input"
  
                    @blur="formatNumberOnBlurOutsource(scope.row, 'housefund-comppart')"></el-input>
                  <div v-else class="item__txt">{{formatNumber(scope.row['housefund-comppart'])}}</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="housefund-personpart" header-align="center" align="right" width="100" label="公积金个人">
              <template slot-scope="scope">
                <div class="item">
                  <el-input 
                    v-if="scope.row.isEditing === true"
                    v-model="scope.row['housefund-personpart']" 
                    placeholder="请输入内容"
                    v-number
                    class="number-input"
                  
                    @blur="formatNumberOnBlurOutsource(scope.row, 'housefund-personpart')"></el-input>
                  <div v-else class="item__txt">{{formatNumber(scope.row['housefund-personpart'])}}</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="houseFundTotal" header-align="center" align="right" width="100" label="公积金合计"></el-table-column>
            <el-table-column prop="disableMoney" header-align="center" align="right" width="100" label="残保金">
              <template slot-scope="scope">
                <div class="item">
                  <el-input 
                    v-if="scope.row.isEditing === true"
                    v-model="scope.row['disableMoney']" 
                    placeholder="请输入内容"
                    v-number
                    class="number-input"
                  
                    @blur="formatNumberOnBlurOutsource(scope.row, 'disableMoney')"></el-input>
                  <div v-else class="item__txt">{{formatNumber(scope.row['disableMoney'])}}</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="allPartTotal" header-align="center" align="right" width="100" label="全部合计"></el-table-column>
            
            <el-table-column header-align="center" align="center" width="120" label="操作" :fixed="getTableScrollStatus('outsource-table') ? 'right' : false">
             <template slot-scope="scope">
                <!-- 编辑模式下显示 -->
                <template v-if="scope.row.isEditing === true">
                  <el-button type="text" class="status-link" @click="saveRowEdit(scope.row, 'outsource')">确定</el-button>
                  <el-button type="text" class="status-link" @click="cancelRowEdit(scope.row)">取消</el-button>
                </template>
                
                <!-- 非编辑模式下显示 -->
                <template v-else>
                  <el-tooltip :disabled="ansStatus != 0" :content="hintMsg" placement="top">
                    <el-button 
                     :disabled="ansStatus == 0"
                     type="text" class="status-link" @click="editRow(scope.row)">修改</el-button>
                  </el-tooltip>
                  <el-tooltip :disabled="ansStatus != 0" :content="hintMsg" placement="top">
                    <el-button 
                      :disabled="ansStatus == 0"
                      type="text" class="status-link" @click="handleDelete(scope.row,'outsource')">删除</el-button>
                  </el-tooltip>
                  
                </template>
              </template>
            </el-table-column>
          </el-table>
          </div>
  
         
    
        </el-tab-pane>


        <el-tab-pane label="工资单" name="third">
          <div class="table-container"> 
            <div class="top-bar">
            <div class="action-buttons">
              <el-tooltip :disabled="ansStatus != 0" :content="hintMsg" placement="top">
                <el-button :disabled="ansStatus == 0" type="primary" @click="addEmp('payroll')">新增员工</el-button>
              </el-tooltip>
              
              <!-- <el-button type="info" @click="showDoc('payroll')">参数</el-button> -->
              <el-tooltip :disabled="ansStatus != 0" :content="hintMsg" placement="top">
                <el-button 
                  :disabled="ansStatus == 0"
                  class="btn-white" @click="frontGenerate('payroll')">重置数据</el-button>
              </el-tooltip>
              <el-tooltip :disabled="ansStatus != 0" :content="hintMsg" placement="top">
                <el-button 
                  :disabled="ansStatus == 0"
                  class="btn-white" @click="uploadDetailPersonalTax">上传个税回执</el-button>
              </el-tooltip>
              <el-button class="btn-white" @click="exportPayrollExcel">导出</el-button>
              <el-tooltip :disabled="ansStatus != 0" :content="hintMsg" placement="top">
                <div v-if="!isPayrollConfirmed" 
                  :class="ansStatus ==0 ? 'confirm-status-disabled' : 'confirm-status'" 
                    @click="ansStatus ==0 ? null : confirmData('payroll')">
                  <i class="el-icon-time"></i>
                  <span>待确认</span>
                </div>
              </el-tooltip>
              
            </div>
            <div class="total-amount">
              <!-- 本期需发放 <span class="amount-value">{{ formatAmount(calculatePayrollTotalAmount() || 0) }}</span> 元 -->
              <el-button type="primary" @click="showPayrollDeclareDialog = true" style="margin-left: 16px;">申报数据</el-button>
            </div>
          </div>
          
          <el-table
            v-loading="loading"
            height="100%"
            show-summary
            :summary-method="getSummaries"
            :data="payrollData"
            row-key="id"
            border
            id="payroll-table"
            default-expand-all
            @selection-change="payrollSelectionChangeHandle"
            highlight-current-row
            :row-style="{height:'30px'}"
            :cell-style="{padding:'5px'}"
          >
          
            <el-table-column type="index" label="序号" align="center" width="50"> </el-table-column>
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column prop="empdoc_name" header-align="center" align="center" width="90" label="姓名"></el-table-column>
            <el-table-column prop="dept_name" header-align="center" align="center" width="80" label="部门"></el-table-column>
           
            <el-table-column
                v-for="(column, index) in payrollItems"
                :key="index"
                :prop="column.code"
                :label="column.name"
                :width="isInsuranceColumn(column) || isPersonalTaxColumn(column) ? 140 : 92"
                header-align="center" align="right" show-overflow-tooltip>
                <template slot-scope="scope">
                  <div class="item">
                    <el-input 
                      v-if="scope.row.isEditing === true && payrollEditProp.includes(column.code)"
                      v-model="scope.row[column.code]" 
                      placeholder="请输入内容"
                      v-number
                      class="number-input"
                     
                      @blur="formatNumberOnBlurPayroll(scope.row, column.code)"></el-input>
                    <div v-else class="item__txt">
                      <!-- 保险扣款列特殊显示 -->
                      <template v-if="isInsuranceColumn(column)">
                        <div class="status-row">
                          <span class="amount">{{ formatNumber(scope.row[column.code]) }}</span>
                          <span
                            v-if="!isInsuranceConfirmed || !isHousefundConfirmed || (outsourceData && outsourceData.length > 0 && !isOutsourceConfirmed)"
                            class="unconfirmed-tag"
                          >
                            未确认
                          </span>
                        </div>
                      </template>
                      <!-- 个税列特殊显示 -->
                      <template v-else-if="isPersonalTaxColumn(column)">
                        <div class="status-row">
                          <span class="amount">{{ formatNumber(scope.row[column.code]) }}</span>
                          <span v-if="!isPersonalTaxUploaded(scope.row)" class="unconfirmed-tag">待上传</span>
                        </div>
                      </template>
                      <!-- 其他列正常显示 -->
                      <template v-else>
                        {{ formatNumber(scope.row[column.code]) }}
                      </template>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column header-align="center" align="center" width="160" label="操作" :fixed="getTableScrollStatus('payroll-table') ? 'right' : false">
                <template slot-scope="scope">
                  <!-- 编辑模式下显示 -->
                  <template v-if="scope.row.isEditing === true">
                    <el-button type="text" class="status-link" @click="saveRowEdit(scope.row, 'payroll')">确定</el-button>
                    <el-button type="text" class="status-link" @click="cancelRowEdit(scope.row)">取消</el-button>
                  </template>
                  
                  <!-- 非编辑模式下显示 -->
                  <template v-else>
                    <el-tooltip :disabled="ansStatus != 0" :content="hintMsg" placement="top">
                      <el-button 
                      :disabled="ansStatus == 0"
                      type="text" class="status-link" @click="editRow(scope.row)">修改</el-button>
                    </el-tooltip>
                    <el-tooltip :disabled="ansStatus != 0" :content="hintMsg" placement="top">
                      <el-button 
                      :disabled="ansStatus == 0"
                      type="text" class="status-link" @click="handleLeave(scope.row,'payroll')">离职</el-button>
                    </el-tooltip>
                    <el-tooltip :disabled="ansStatus != 0" :content="hintMsg" placement="top">
                      <el-button 
                      :disabled="ansStatus == 0"
                      type="text" class="status-link" @click="handleDelete(scope.row,'payroll')">删除</el-button>
                    </el-tooltip>
                  </template>
                </template>
              </el-table-column>
          </el-table>
          </div>
  
          
      
        </el-tab-pane>
        
      </el-tabs>
           
      </div>
      <!-- <div slot="footer" class="dialog-footer" style="display: none;">
      <el-button @click="handleClose">关闭</el-button>
    </div> -->
    <div slot="footer" class="dialog-footer">
      <el-button class="btn-white" @click="handleClose">关闭</el-button>
      <el-tooltip :disabled="ansStatus != 0" :content="hintMsg" placement="top">
        <el-button 
        :disabled="ansStatus == 0"
        type="primary" @click="confirmData()">提交并确认数据</el-button>
      </el-tooltip>
    </div>
    <insurance-parameter   ref="insuranceParameter"  @update:visible="paraVisible2 = $event" 
      v-if="paraVisible2"></insurance-parameter>
    <emp-add ref="empAdd" @refresh="refreshPage"></emp-add>
    <!-- 弹窗, 新增 / 修改 -->
    <template-select ref="templateSelect" @continue-upload="handleContinueUpload"></template-select>
    <add-template ref="addTemplate"  @continue-upload="handleContinueUpload"></add-template>

    <emp-confirm ref="empConfirm" @continue-upload="handleContinueUpload"></emp-confirm>
    <insurance-adjust-base   ref="insuranceAdjustBase" @refresh="getInsuranceDataList"></insurance-adjust-base>
    <housefund-adjust-base   ref="housefundAdjustBase" @refresh="getHousefundDataList"></housefund-adjust-base>
    <outsource-adjust-base   ref="outsourceAdjustBase" @refresh="getOutsourceDataList"></outsource-adjust-base>
    <payroll-declare-dialog
      :visible.sync="showPayrollDeclareDialog"
      :payroll-data="declareTableData"
      className="payroll-declare-dialog"
      @close="showPayrollDeclareDialog = false"
    />

    <file-uploader3 
      ref="fileUploader3"
      v-if="uploadVisible"
    :visible="uploadVisible" @update:visible="uploadVisible = $event" 
    :url="getUploadUrl()"></file-uploader3>

  </el-dialog>
  
  </template>
  
  <script>
  import moment from 'moment'
  import * as XLSX from 'xlsx';
  import { saveAs } from 'file-saver';
  import { checkEm } from '../../../utils/validate';
  import insuranceParameter from './insuranceParameter.vue'
  import templateSelect from './template-select.vue'
  import addTemplate from './add-template.vue'
  import empConfirm from './emp-confirm.vue'
  import empAdd from './emp-add.vue'
  import socket from '@/utils/socket'
import insuranceAdjustBase from './insuranceAdjustBase.vue'
import housefundAdjustBase from './housefundAdjustBase.vue'
import outsourceAdjustBase from './outsourceAdjustBase.vue'
import PayrollDeclareDialog from './PayrollDeclareDialog.vue'
import fileUploader3 from '../components/file-uploader3.vue'
import tableScrollMixin from '@/mixins/tableScrollMixin'
import { getAnsStatus } from '@/utils/common.js'
// 在组件中导入并使用
import dialogContentCalculate from '@/mixins/dialogContentCalculate'
import { formatNumber } from '@/utils/numberUtils.js';
  export default {
    mixins: [tableScrollMixin,dialogContentCalculate],
    
    data () {
      return {
        ansStatus:0,
        hintMsg:'',
        loading:false,
        visible: false,
        accountCode: '',
        accountid:null,
        currentMonth:'',
        nature:'',
        saleFormInline:{},

        insuranceDataListSelections: [],
        insuranceData:[],
        insuranceItems:[],
        insuranceComppartItems:[],
        insurancePersonpartItems:[],
        insuranceActual:'',
        insuranceTotalMoney:0,
        isInsuranceConfirmed: false,

        housefundDataListSelections:[],
        housefundData:[],  
        housefundItems:[],
        housefundComppartItems:[],
        housefundPersonpartItems:[],
        housefundActual:'',

        outsourceDataListSelections: [],
        outsourceData:[],
        outsourceItems:[],
        outsourceComppartItems:[],
        outsourcePersonpartItems:[],
        outsourceActual:'',
        isOutsourceConfirmed: false,



        payrollDataListSelections:[],
        payrollData:[],  
        payrollItems:[],
        payrollId:'',
        activeName: 'first',
        
        insuranceEditProp: [],
        insuranceClickCellMap: {},
        insuranceEditFlag: false,

        housefundEditProp: [],
        housefundClickCellMap: {},
        housefundEditFlag: false,

        outsourceEditProp: [],
        outsourceClickCellMap: {},
        outsourceEditFlag: false,

        payrollEditProp: [],
        payrollClickCellMap: {},
        payrollEditFlag: false,

        templateSelectVisible:false,
        
        // 当前正在编辑的行
        currentEditingRow: null,
        isHousefundConfirmed: false,
        isPayrollConfirmed: false,
        // 个税回执是否已上传
        isTaxReceiptUploaded: false,
        adjustBaseDialogVisible: false,
        adjustBaseTableData: [],
        adjustBaseType: '', // insurance/housefund/outsource
        showPayrollDeclareDialog: false,
        uploadVisible: false,
        sheetSelectDialogVisible: false,
        sheetNames: [],
        selectedSheetName: '',
        paraVisible2:false,
   
      }
    },
    components: {
      insuranceParameter,
      empAdd,
      templateSelect,
      addTemplate,
      empConfirm,
      insuranceAdjustBase,
      housefundAdjustBase,
      outsourceAdjustBase,
      PayrollDeclareDialog,
      fileUploader3,
    },
    methods: {
      async checkAnsStatus() {
        const result = await getAnsStatus(this.$http, this.accountid);
        this.ansStatus = result.status;
        this.hintMsg = result.message;
      },
      show(row, tabName, currentMonth) {
        // 重置所有编辑状态相关的变量
        this.currentEditingRow = null;
        this.accountid = row.accountId;
        this.accountCode = row.accountCode;
        this.currentMonth = currentMonth;
        this.housefundActual = row.housefundActual;
        this.insuranceActual = row.insuranceActual;
        // 根据传入的数据判断是否已确认
        this.isInsuranceConfirmed = row.insurance === 1 || row.insurance === '1' || row.insurance === '已确认';
        this.isHousefundConfirmed = row.housefund === 1 || row.housefund === '1' || row.housefund === '已确认';
        this.isPayrollConfirmed = row.payroll === 1 || row.payroll === '1' || row.payroll === '已确认';
        this.isOutsourceConfirmed = row.outsource === 1 || row.outsource === '1' || row.outsource === '已确认';
        
        this.visible = true;
        
        // 使用$nextTick确保对话框已渲染，再使用ID选择器获取高度（最可靠方式）
        this.$nextTick(() => {
          // 给更多时间让对话框完全渲染并计算尺寸
          setTimeout(() => {
            // 使用ID选择器（推荐）
            this.initTableHeight('salary-module-insurance-preview');
            this.checkAnsStatus()
            // 也可以直接检查对话框元素是否存在
            const dialogElement = document.querySelector('salary-module-insurance-preview');
            console.log('对话框元素:', dialogElement);
            const dialogBody = document.querySelector('salary-module-insurance-preview .el-dialog__body');
            console.log('对话框主体元素:', dialogBody, dialogBody ? dialogBody.offsetHeight : 'undefined');
          }, 300);  // 增加延时，确保完全渲染
        });
        
        let tableName = tabName || 'first';
        this.activeName = tableName;
        if(tableName == 'first'){
          this.getInsuranceDataList();
        } else if(tableName == 'second'){
          this.getHousefundDataList();
        } else if(tableName == 'third'){
          // 先查社保、公积金、外包保险，再查工资单
          Promise.all([
            this.getInsuranceDataList(),
            this.getHousefundDataList(),
            this.getOutsourceDataList()
          ]).then(() => {
            this.getPayrollDataList();
          });
        } else if(tableName == 'fourth'){
          this.getOutsourceDataList();
        }
      },
      uploadDetailPersonalTax(){
        this.uploadVisible = true;
        this.$nextTick(() => {
          this.$refs.fileUploader3.show()
        })
      },
      getUploadUrl(){
        return this.$http.adornUrl(`/salary/salarystatus/parsePersonalTaxExcel/${this.accountCode}`);
      },
      
      // 检查工资项目是否都为0或空
      isAllPayrollItemsZeroOrEmpty(row) {
        if (!row || !this.payrollItems || this.payrollItems.length === 0) {
          return false;
        }
        
        // 获取所有工资项的编码
        const payrollCodes = this.payrollItems.map(item => item.code);
        
        // 检查每个工资项是否都为0或空
        return payrollCodes.every(code => {
          const value = row[code];
          return value === '' || value === null || value === undefined || parseFloat(value) === 0;
        });
      },
      
      formatNumberOnBlurInsurance(row, property) {
        // 获取当前项目的精度和进位方式
        debugger
        let itemAccuracy = 0;
        let pointHandle = 0;
        
        // 从保险项目中查找对应的配置
        this.insuranceItems.forEach(item => {
          if(item.insuranceitemscode === property) {
            itemAccuracy = item.accuracy || 0;
            pointHandle = item.pointhandle || 0;
          }
        });

        // 获取当前值
        let value = parseFloat(row[property] || 0);
        
        // 根据进位方式处理数值
        let result;
        switch(pointHandle) {
          case 1: // 去位
            result = Math.floor(value * Math.pow(10, itemAccuracy)) / Math.pow(10, itemAccuracy);
            break;
          case 2: // 进位
            result = Math.ceil(value * Math.pow(10, itemAccuracy)) / Math.pow(10, itemAccuracy);
            break;
          default: // 四舍五入
            result = Math.round(value * Math.pow(10, itemAccuracy)) / Math.pow(10, itemAccuracy);
        }

        // 设置处理后的值
        this.$set(row, property, result);

        // 计算合计值
        let compparttotal = 0;
        let personparttotal = 0;
        let totalparttotal = 0;

        // 遍历所有项目计算合计
        this.insuranceItems.forEach(item => {
          const value = parseFloat(row[item.insuranceitemscode] || 0);
          if(item.insuranceitemscode.indexOf('-comppart') !== -1) {
            compparttotal += value;
            totalparttotal += value;
          } else if(item.insuranceitemscode.indexOf('-personpart') !== -1) {
            personparttotal += value;
            totalparttotal += value;
          }
        });

        // 设置合计值
        this.$set(row, 'compparttotal', compparttotal.toFixed(2));
        this.$set(row, 'personparttotal', personparttotal.toFixed(2));
        this.$set(row, 'totalparttotal', totalparttotal.toFixed(2));
      },
      formatNumberOnBlurHousefund(row, property) {
        debugger
        // 获取当前项目的精度和进位方式
        let itemAccuracy = 0;
        let pointHandle = 0;
        
        // 从公积金项目中查找对应的配置
        this.housefundItems.forEach(item => {
          if(item.insuranceitemscode === property) {
            itemAccuracy = item.accuracy || 0;
            pointHandle = item.pointhandle || 0;
          }
        });

        // 获取当前值
        let value = parseFloat(row[property] || 0);
        
        // 根据进位方式处理数值
        let result;
        switch(pointHandle) {
          case 1: // 去位
            result = Math.floor(value * Math.pow(10, itemAccuracy)) / Math.pow(10, itemAccuracy);
            break;
          case 2: // 进位
            result = Math.ceil(value * Math.pow(10, itemAccuracy)) / Math.pow(10, itemAccuracy);
            break;
          default: // 四舍五入
            result = Math.round(value * Math.pow(10, itemAccuracy)) / Math.pow(10, itemAccuracy);
        }

        // 设置处理后的值
        this.$set(row, property, result);

        // 计算合计值
        let compparttotal = 0;
        let personparttotal = 0;
        let totalparttotal = 0;

        // 遍历所有项目计算合计
        this.housefundItems.forEach(item => {
          const value = parseFloat(row[item.insuranceitemscode] || 0);
          if(item.insuranceitemscode.indexOf('-comppart') !== -1) {
            compparttotal += value;
            totalparttotal += value;
          } else if(item.insuranceitemscode.indexOf('-personpart') !== -1) {
            personparttotal += value;
            totalparttotal += value;
          }
        });

        // 设置合计值
        this.$set(row, 'compparttotal', compparttotal.toFixed(itemAccuracy));
        this.$set(row, 'personparttotal', personparttotal.toFixed(itemAccuracy));
        this.$set(row, 'totalparttotal', totalparttotal.toFixed(itemAccuracy));
      },
      formatNumberOnBlurOutsource(row, property, accuracy=2){
    
        debugger
      let value = (parseFloat(row[property] || 0)).toFixed(accuracy);
      this.$set(row,property,value);
      let itemProp = this.outsourceEditProp;

      //取消编辑，重算当前行的个人合计，单位合计，合计数据
      let compparttotal = 0;
      let personparttotal = 0;
      let totalparttotal = 0;
      let housefundtotal =0;
  

      let allparttotal = 0;

      //遍历row 中的数据，计算个人合计，单位合计，合计数据
      //如果insuranceitemscode
      for(let i in itemProp){
        if(itemProp[i].indexOf('-comppart') !=-1 || itemProp[i].indexOf('-personpart') !=-1){
          //
          if(itemProp[i] =='housefund-comppart' || itemProp[i] =='housefund-personpart'){
            let money = row[itemProp[i]] || 0;
            housefundtotal = housefundtotal  + parseFloat(money);
            allparttotal = allparttotal + parseFloat(money);
          }else{
          
            if(itemProp[i].indexOf('-comppart') !=-1){
              let comppart = row[itemProp[i]] || 0;
              compparttotal = compparttotal + parseFloat(comppart);
              totalparttotal = totalparttotal + parseFloat(comppart);
              allparttotal = allparttotal + parseFloat(comppart);

            }else if(itemProp[i].indexOf('-personpart') !=-1){
              let personpart = row[itemProp[i]] || 0;
              personparttotal = personparttotal + parseFloat(personpart);
              totalparttotal = totalparttotal + parseFloat(personpart);
              allparttotal = allparttotal + parseFloat(personpart);
            }
          }

        }else if(itemProp[i] == 'disableMoney'){
          let money = row[itemProp[i]] || 0;
          allparttotal = allparttotal + parseFloat(money);
        }

        }
        this.$set(row,'compPartTotal',compparttotal.toFixed(accuracy));
        this.$set(row,'personPartTotal',personparttotal.toFixed(accuracy));
        this.$set(row,'insurancePartTotal',totalparttotal.toFixed(accuracy));
        this.$set(row,'houseFundTotal',housefundtotal.toFixed(accuracy));
        this.$set(row,'allPartTotal',allparttotal.toFixed(accuracy));

        // row.compPartTotal = compparttotal.toFixed(accuracy);
        // row.personPartTotal = personparttotal.toFixed(accuracy);
        // row.insurancePartTotal = totalparttotal.toFixed(accuracy);

        // row.houseFundTotal = housefundtotal.toFixed(accuracy);
        // row.allPartTotal = allparttotal.toFixed(accuracy);
      },
      
      
    formatNumberOnBlurPayroll(row,property){
    
    debugger
    let itemProp = this.payrollItems;
   // const property = column.property
      let value = parseFloat(row[property] || 0);
    // 根据进位方式处理数值
      let result = Math.round(value * Math.pow(10, 2)) / Math.pow(10, 2);
       this.$set(row, property, result);
      this.$set(row,property,value);
      for(let i in itemProp){
        let datasource = itemProp[i]['datasource'];
        let calformula = itemProp[i]['calformula'].replaceAll('[','[salaryitemscode_');;
        let code = itemProp[i]['code'];
        //&& calformula.indexOf('['+property+']') !=-1
        if(datasource == '计算公式' && calformula !='' ){//是计算公式的都计算一下
          
          for(let j in row){
            let calkey='['+j+']';
            calformula=calformula.replace(calkey,(row[j]==null?0:Number(row[j])));
          }
          if(calformula.indexOf('[') !=-1 && calformula.indexOf(']') !=-1){
            var myReg= /\[(.*?)\]/g;
            var aa=calformula.match(myReg);
            calformula=calformula.replace(myReg, "0");
          }
          calformula= calformula.replace(/\+\+/g,"+");
          calformula= calformula.replace(/\+\-/g,"-");
          calformula= calformula.replace(/\-\+/g,"-");
          calformula= calformula.replace(/\-\-/g,"+");
          let value=parseFloat(eval(calformula)).toFixed(2);

          this.$set(row,code,value);

        }
      }
  },
      // 判断是否为保险扣款列
      isInsuranceColumn(column) {
        if (!column || !column.code || !this.payrollItems) return false;
        const insuranceItem = this.payrollItems.find(item => 
          item.code === column.code && item.datasource === '保险费扣除'
        );
        return !!insuranceItem;
      },
      
      // 判断是否为个税列
      isPersonalTaxColumn(column) {
        if (!column || !column.code || !this.payrollItems) return false;
        const taxItem = this.payrollItems.find(item => 
          item.code === column.code && item.name === '个税'
        );
        return !!taxItem;
      },
      // 格式化金额显示
      formatAmount(value) {
        if (!value && value !== 0) return '0.00';
        return Number(value).toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
      },
      
      // 计算总金额
      calculateTotalAmount() {
        if (!this.insuranceData || this.insuranceData.length === 0) return 0;
        
        let total = 0;
        this.insuranceData.forEach(item => {
          if (item.totalparttotal) {
            total += parseFloat(item.totalparttotal);
          }
        });
        
        return total;
      },

      // 计算总金额
      calculateHousefundTotalAmount() {
        if (!this.housefundData || this.housefundData.length === 0) return 0;
        
        let total = 0;
        this.housefundData.forEach(item => {
          if (item.totalparttotal) {
            total += parseFloat(item.totalparttotal);
          }
        });
        
        return total;
      },
      
      /**
       * 编辑行数据
       * 优化版：使用Vue的响应式系统，确保isEditing属性变更正确触发重渲染
       */
      editRow(row) {
        try {
          // 如果有其他行正在编辑，先取消编辑
          if (this.currentEditingRow && this.currentEditingRow !== row) {
            this.cancelRowEdit(this.currentEditingRow);
          }
          
          // 创建编辑前数据的深拷贝，用于取消编辑时恢复
          const originalData = JSON.parse(JSON.stringify(row));
          
          // 使用Vue的响应式系统进行更新
          // 先设置原始数据备份
          this.$set(row, '_originalData', originalData);
          
          // 明确设置编辑状态为true，确保Vue能检测到变化
          this.$set(row, 'isEditing', true);
          
          // 保存当前编辑行的引用
          this.currentEditingRow = row;
          
          // 强制组件重新渲染以确保UI更新
          this.$nextTick(() => {
            this.$forceUpdate();
            console.log('Row editing started, isEditing:', row.isEditing);
          });
        } catch (error) {
          console.error('Error in editRow:', error);
        }
      },
      
      saveRowEdit(row, type) {
        try {
          console.log('Saving row edit, type:', type, 'isEditing before:', row.isEditing);
          
          // 确保row对象存在
          if (!row) {
            console.error('Row object is undefined in saveRowEdit');
            return;
          }
          
          // 根据类型执行特定的保存逻辑
          if (type === 'insurance') {
            // 计算合计值
            
            // 保存到后台
            this.saveRowToBackend(row);
          } else if (type === 'housefund') {
            // 处理公积金相关的保存逻辑
            this.saveHousefundRowToBackend(row);
          } else if (type === 'outsource') {
            // 保存到后台
            this.saveOutsourceRowToBackend(row);
          } else if (type === 'payroll') {
         
            //去除row中的_originalData属性
            if (row._originalData) {
              delete row._originalData;
            }
            // 保存到后台
            // 内联实现工资单保存逻辑，替代对savePayrollRowToBackend的调用
            try {
              // 显示加载状态
              this.loading = true;
              
              // 发送请求到后台保存修改后的数据
              this.$http({
                url: this.$http.adornUrl('/salary/payroll/updateSingleRow'),
                method: 'post',
                data: this.$http.adornData({
                  'id': this.payrollId || undefined,
                  'accountCode': this.accountCode,
                  'payrollmonth': this.currentMonth,
                  'itemNames': JSON.stringify(this.payrollItems),
                  'payrollmxdata': JSON.stringify(row)
                })
              }).then(({data}) => {
                this.loading = false;
                if (data && data.code === 0) {
                  this.$message({
                    message: '保存成功',
                    type: 'success',
                    duration: 1500
                  });
                  // 刷新数据列表
                  this.getPayrollDataList();
                } else {
                  this.$message.error(data.msg || '保存失败');
                }
              }).catch((error) => {
                this.loading = false;
                console.error('Error saving payroll row:', error);
                this.$message.error('网络错误，请稍后重试');
              });
            } catch (saveError) {
              console.error('Error in payroll save process:', saveError);
              this.loading = false;
            }
          }
          
          // 移除临时备份数据
          this.$delete(row, '_originalData');
          
          // 明确设置编辑状态为false
          this.$set(row, 'isEditing', false);
          
          // 清除当前编辑行引用
          this.currentEditingRow = null;
          
          // 强制组件重新渲染
          this.$nextTick(() => {
            this.$forceUpdate();
            console.log('Row edit saved, isEditing after:', row.isEditing);
          });
        } catch (error) {
          console.error('Error in saveRowEdit:', error);
        }
      },
      
      cancelRowEdit(row) {
        try {
          console.log('Cancelling row edit, isEditing before:', row.isEditing);
          
          // 恢复原始数据
          if (row._originalData) {
            // 遍历原始数据的所有属性并逐个恢复
            Object.keys(row._originalData).forEach(key => {
              // 跳过特殊属性
              if (key !== 'isEditing' && key !== '_originalData') {
                this.$set(row, key, row._originalData[key]);
              }
            });
          }
          
          // 移除临时备份数据
          this.$delete(row, '_originalData');
          
          // 明确设置编辑状态为false
          this.$set(row, 'isEditing', false);
          
          // 清除当前编辑行引用
          this.currentEditingRow = null;
          
          // 强制组件重新渲染
          this.$nextTick(() => {
            this.$forceUpdate();
            console.log('Row edit cancelled, isEditing after:', row.isEditing);
          });
        } catch (error) {
          console.error('Error in cancelRowEdit:', error);
        }
      },
      
      saveRowToBackend(row) {
        // 显示加载状态
        this.loading = true;
        
        // 发送请求到后台保存修改后的数据
        this.$http({
          url: this.$http.adornUrl('/salary/insurance/updateSingleRow'),
          method: 'post',
          data: this.$http.adornData({
            'id': this.insuranceId || undefined,
            'accountCode':this.accountCode,
            'insurancemonth': this.currentMonth,
            'itemNames': JSON.stringify(this.insuranceItems),
            'calinsurancenewmxdata': JSON.stringify(row)
          })
        }).then(({data}) => {
          this.loading = false;
          if (data && data.code === 0) {
            this.$message({
              message: '保存成功',
              type: 'success',
              duration: 1500
            });
            // 确保此行的编辑状态被设置为false
            this.$set(row, 'isEditing', false);
            // 清除当前编辑行引用
            this.currentEditingRow = null;
            // 强制组件重新渲染
            this.$nextTick(() => {
              this.$forceUpdate();
            });
            // 刷新数据
            this.getInsuranceDataList();
          } else {
            this.$message.error(data.msg || '保存失败');
          }
        }).catch((error) => {
          this.loading = false;
          console.error('Error saving row:', error);
          this.$message.error('网络错误，请稍后重试');
        });
      },
      
      saveHousefundRowToBackend(row) {
        // 显示加载状态
        this.loading = true;
        
        // 发送请求到后台保存修改后的数据
        this.$http({
          url: this.$http.adornUrl('/salary/housefund/updateSingleRow'),
          method: 'post',
          data: this.$http.adornData({
            'id': this.housefundId || undefined,
            'accountCode':this.accountCode,
            'insurancemonth': this.currentMonth,
            'itemNames': JSON.stringify(this.insuranceItems),
            'calhousefundnewmxdata': JSON.stringify(row)
          })
        }).then(({data}) => {
          this.loading = false;
          if (data && data.code === 0) {
            this.$message({
              message: '保存成功',
              type: 'success',
              duration: 1500
            });
            // 刷新数据
            this.getHousefundDataList();
          } else {
            this.$message.error(data.msg || '保存失败');
          }
        }).catch((error) => {
          this.loading = false;
          console.error('Error saving housefund row:', error);
          this.$message.error('网络错误，请稍后重试');
        });
      },

         // 保存外包保险单行数据
     saveOutsourceRowToBackend(row) {
       // 显示加载状态
       this.loading = true;
    
       // 发送请求到后台保存修改后的数据
       this.$http({
         url: this.$http.adornUrl('/salary/outsource/updateSingleRow'),
         method: 'post',
         data: this.$http.adornData({
           'id': this.outsourceId || undefined,
           'accountCode': this.accountCode,
           'insurancemonth': this.currentMonth,
           'itemNames': JSON.stringify(this.outsourceItems),
           'caloutsourcemxdata': JSON.stringify(row)
         })
       }).then(({data}) => {
         this.loading = false;
         if (data && data.code === 0) {
           this.$message({
             message: '保存成功',
             type: 'success',
             duration: 1500
           });
           // 刷新数据列表
           this.getOutsourceDataList();
         } else {
           this.$message.error(data.msg || '保存失败');
         }
       }).catch((error) => {
         this.loading = false;
         console.error('Error saving outsource row:', error);
          this.$message.error('网络错误，请稍后重试');
        });
      },
      

      handleClose(){
        this.visible = false
        this.accountCode = ''
        this.currentMonth = ''
        this.nature = ''
        this.type = ''
        
        this.insuranceId = ''
        this.insuranceData = []
        this.insuranceItems =[]
        this.insuranceComppartItems =[]
        this.insurancePersonpartItems = []
        this.insuranceEditProp=[]
        this.insuranceClickCellMap={}
        this. insuranceEditFlag=false
        this. insuranceActual=''
       
        this.housefundId = ''
        this.housefundData = []
        this.houfundItems = []
        this.housefundComppartItems =[]
        this.housefundPersonpartItems = []
        this.housefundEditProp=[]
        this.housefundClickCellMap={}
        this. housefundEditFlag=false
        this. housefundActual=''


        this.outsourceId = ''
        this.outsourceData = []
        this.outsourceItems = []
        this.outsourceComppartItems =[]
        this.outsourcePersonpartItems = []
        this.outsourceEditProp=[]
        this.outsourceClickCellMap={}
        this.outsourceEditFlag=false
        this.outsourceActual=''



        this.payrollData = []
        this.payrollItems =[]
        this.payrollId = ''
        this.payrollEditProp=[]
        this.payrollClickCellMap={}
        this.payrollEditFlag=false

        this.$emit('update:visible',false)
        
        this.$parent.$parent.getDataList();
     },
     refreshPage(type){
      if(type==='payroll'){
                    this.getPayrollDataList(); // 刷新数据列表
                  }else if(type==='housefund'){
                    this.getHousefundDataList();
                  }else if(type==='insurance'){
                    this.getInsuranceDataList();
                  }else if(type==='outsource'){
                    this.getOutsourceDataList();
                  }
     },
     confirmData(){
        let tabName = this.activeName;
        let type='';
        if(tabName == 'first'){
          type='insurance';
        }else if(tabName == 'second'){
          type='housefund';
        }else if(tabName == 'third'){
          type='payroll';
        }else if(tabName == 'fourth'){
          type='outsource';
        }
      let alertMessage = "是否确认当前数据为本月正确数据?";
        this.$confirm(alertMessage, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // 发起HTTP请求
          this.$http({
            url: this.$http.adornUrl(`/salary/salarystatus/handleConfirm/${this.accountCode}/${this.currentMonth}/${type}`),
            method: 'put',
     
          }).then(({ data }) => {
            // 处理响应
            if (data && data.code === 0) {
              // 操作成功
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  if(type==='payroll'){
                    // 更新确认状态
                    this.isPayrollConfirmed = true;
                    this.getPayrollDataList(); // 刷新数据列表
                  }else if(type==='housefund'){
                    // 更新确认状态
                    this.isHousefundConfirmed = true;
                    this.getHousefundDataList();
                  }else if(type==='insurance'){
                    // 更新确认状态
                    this.isInsuranceConfirmed = true;
                    this.getInsuranceDataList();
                  }else if(type==='outsource'){
                    this.isOutsourceConfirmed = true;
                    this.getOutsourceDataList();
                  }
                 
                }
              });
            } else {
              // 操作失败
              this.$message.error(data.msg);
            }
          });
        }).catch(() => {
          
        });

     },
     calculateOutsourceTotalAmount() {
       if (!this.outsourceData || this.outsourceData.length === 0) return 0;
       
       let total = 0;
       this.outsourceData.forEach(item => {
         if (item.allPartTotal) {
           total += parseFloat(item.allPartTotal);
         }
       });
       
       return total;
     },
     saleSelectionChangeHandle(){},
     
     // 通用Excel导出函数，将数据导出为Excel文件
     exportToExcel(data, fileName) {
       try {
        // 创建工作簿和工作表
        const wb = XLSX.utils.book_new();
         const ws = XLSX.utils.aoa_to_sheet(data);
        
        // 设置列宽 - 动态计算合适的列宽
        const colWidths = [];
        
        // 默认列宽设置
         for (let i = 0; i < (data[0] ? data[0].length : 0); i++) {
          // 计算列数据的最大长度
          let maxLength = 8; // 默认最小宽度
          
          // 遍历所有行，找出每列的最大长度
           for (let j = 0; j < data.length; j++) {
             if (data[j][i]) {
               const cellValue = String(data[j][i] || '');
              maxLength = Math.max(maxLength, cellValue.length);
            }
          }
          
          // 设置列宽（每个字符约为1个字符宽度）
          // 宽度上限为20，下限为8
          colWidths.push({ wch: Math.min(20, Math.max(8, maxLength + 2)) });
        }
        
        ws['!cols'] = colWidths;
        
         // 设置表头样式 - 橙色背景
         if (data.length > 0) {
           // 表头行
           const headerRange = { s: { r: 0, c: 0 }, e: { r: 0, c: data[0].length - 1 } };
           
           // 遍历表头范围内的所有单元格
           for (let R = headerRange.s.r; R <= headerRange.e.r; R++) {
             for (let C = headerRange.s.c; C <= headerRange.e.c; C++) {
              const cell = XLSX.utils.encode_cell({ r: R, c: C });
               if (!ws[cell]) continue;
              
              // 创建或更新单元格样式
               if (!ws[cell].s) ws[cell].s = {};
              
               // 设置橙色背景和粗体
               ws[cell].s.fill = { patternType: 'solid', fgColor: { rgb: "FFA500" } };
              ws[cell].s.font = { bold: true };
              ws[cell].s.alignment = { horizontal: 'center', vertical: 'center' };
            }
          }
        }
        
        // 将工作表添加到工作簿
         XLSX.utils.book_append_sheet(wb, ws, '导出数据');
        
        // 生成 XLSX 文件并保存
         XLSX.writeFile(wb, fileName);
       } catch (error) {
         console.error('导出Excel失败:', error);
         this.$message.error('导出Excel失败，请稍后重试');
       }
     },
     
     // 导出社保数据
     exportInsuraceExcel(){
       let tableHeaders = [];
       let preHeader = [
         { prop: 'empdoc_name', label: '姓名' },
         { prop: 'dept_name', label: '部门' },
         { prop: 'basemoney', label: '申报工资' },
         { prop: 'old_basemoney', label: '养老基数' }
       ];
       
       // 收集单位部分和个人部分标题
       let compHeaders = [];
       let personHeaders = [];
       
       this.insuranceComppartItems.forEach(item => {
         compHeaders.push({ 
           prop: item.insuranceitemscode, 
           label: '单位部分-' + item.insuranceitemsname
         });
       });
       
       this.insurancePersonpartItems.forEach(item => {
         personHeaders.push({ 
           prop: item.insuranceitemscode, 
           label: '个人部分-' + item.insuranceitemsname
         });
       });
       
       let postHeader = [
         { prop: 'compparttotal', label: '单位合计' },
         { prop: 'personparttotal', label: '个人合计' },
         { prop: 'totalparttotal', label: '总合计' }
       ];
       
       // 合并所有表头
       tableHeaders = tableHeaders.concat(preHeader).concat(compHeaders).concat(personHeaders).concat(postHeader);
       
       this.$confirm("此操作将导出社保数据到Excel文件，是否继续？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      ).then(() => {
         // 准备导出数据
        const exportData = [];
        
         // 添加表头行
         const headerRow = tableHeaders.map(header => header.label);
         exportData.push(headerRow);
        
        // 添加数据行
         this.insuranceData.forEach(item => {
           const row = tableHeaders.map(header => {
             return item[header.prop] || '0.00';
           });
           exportData.push(row);
         });
         
         // 添加合计行
         if (this.insuranceData.length > 0) {
           const totalRow = ['合计', '', '', ''];
           
           // 从第5列开始计算合计（跳过姓名、部门、基数等）
           for (let i = 4; i < tableHeaders.length; i++) {
             const columnProp = tableHeaders[i].prop;
             const columnTotal = this.insuranceData.reduce((sum, row) => 
               sum + parseFloat(row[columnProp] || 0), 0);
             totalRow[i] = columnTotal.toFixed(2);
           }
           
           exportData.push(totalRow);
         }
         
         // 调用通用导出函数
         this.exportToExcel(exportData, `${this.accountCode}_${this.currentMonth}_社保明细.xlsx`);
       });
     },
     
     // 导出公积金数据
     exportHousefundExcel(){
       let tableHeaders = [];
       let preHeader = [
         { prop: 'empdoc_name', label: '姓名' },
         { prop: 'dept_name', label: '部门' },
         { prop: 'basemoney', label: '公积金基数' }
       ];
       
       // 收集单位部分和个人部分标题
       let compHeaders = [];
       let personHeaders = [];
       
          this.housefundComppartItems.forEach(item => {
         compHeaders.push({ 
           prop: item.insuranceitemscode, 
           label: '单位部分-' + item.insuranceitemsname
         });
          });
          
          this.housefundPersonpartItems.forEach(item => {
         personHeaders.push({ 
           prop: item.insuranceitemscode, 
           label: '个人部分-' + item.insuranceitemsname
         });
       });
       
       // 添加合计列
       let postHeader = [
         { prop: 'totalparttotal', label: '总合计' }
       ];
       
       // 合并所有表头
       tableHeaders = tableHeaders.concat(preHeader).concat(compHeaders).concat(personHeaders).concat(postHeader);
       
       this.$confirm("此操作将导出公积金数据到Excel文件，是否继续？", "提示", {
           confirmButtonText: "确定",
           cancelButtonText: "取消",
           type: "warning"
         }
       ).then(() => {
         // 准备导出数据
         const exportData = [];
         
         // 添加表头行
         const headerRow = tableHeaders.map(header => header.label);
         exportData.push(headerRow);
         
         // 添加数据行
         this.housefundData.forEach(item => {
           const row = tableHeaders.map(header => {
             return item[header.prop] || '0.00';
           });
           exportData.push(row);
         });
         
         // 添加合计行
         if (this.housefundData.length > 0) {
           const totalRow = ['合计', '', ''];
           
           // 从第4列开始计算合计（跳过姓名、部门、基数）
           for (let i = 3; i < tableHeaders.length; i++) {
             const columnProp = tableHeaders[i].prop;
             const columnTotal = this.housefundData.reduce((sum, row) => 
               sum + parseFloat(row[columnProp] || 0), 0);
             totalRow[i] = columnTotal.toFixed(2);
           }
           
           exportData.push(totalRow);
         }
         
         // 调用通用导出函数
         this.exportToExcel(exportData, `${this.accountCode}_${this.currentMonth}_公积金明细.xlsx`);
      });
     },
     
     // 导出工资单数据
     exportPayrollExcel() {
       let tableHeaders = [];
       let preHeader = [
         { prop: 'empdoc_name', label: '姓名' },
         { prop: 'dept_name', label: '部门' }
       ];
       let itemHeaders=[];
       this.payrollItems.forEach(item => {
         itemHeaders.push({ prop: item.code, label: item.name })
       });
       
       tableHeaders = tableHeaders.concat(preHeader).concat(itemHeaders);
       
       this.$confirm("此操作将导出工资数据到Excel文件，是否继续？", "提示",{
           confirmButtonText: "确定",
           cancelButtonText: "取消",
           type: "warning"
         }
       ).then(() => {
         // 准备导出数据
         const exportData = [];
         
         // 添加表头行（单行表头）
         const headerRow = ['序号', '姓名', '部门'];
         this.payrollItems.forEach(item => {
           headerRow.push(item.name);
         });
         exportData.push(headerRow);
         
         // 添加数据行
         this.payrollData.forEach((item, index) => {
           const row = [
             index + 1,
             item.empdoc_name,
             item.dept_name
           ];
           
           // 添加各个工资项数据
           this.payrollItems.forEach(col => {
             row.push(item[col.code] || '0.00');
           });
           
           exportData.push(row);
         });
         
         // 添加合计行
         if (this.payrollData.length > 0) {
           const totalRow = ['合计', '', ''];
           
           // 计算每列的合计
           this.payrollItems.forEach(col => {
             const columnTotal = this.payrollData.reduce((sum, row) => 
               sum + parseFloat(row[col.code] || 0), 0);
             totalRow.push(columnTotal.toFixed(2));
           });
           
           exportData.push(totalRow);
         }
         
         // 调用通用导出函数
         this.exportToExcel(exportData, `${this.accountCode}_${this.currentMonth}_工资明细.xlsx`);
       });
     },
     
     
     // 导出外包保险数据
     exportOutsourceExcel() {
       let tableHeaders = [];
       let preHeader = [
         { prop: 'empdoc_name', label: '姓名' },
         { prop: 'dept_name', label: '部门' },
         { prop: 'insuranceBasemoney', label: '参保基数' }
       ];
       
       // 收集单位部分和个人部分标题
       let compHeaders = [];
       let personHeaders = [];
       
       this.outsourceComppartItems.forEach(item => {
         compHeaders.push({ 
           prop: item.code, 
           label: '单位部分-' + item.name
         });
       });
       
       this.outsourcePersonpartItems.forEach(item => {
         personHeaders.push({ 
           prop: item.code, 
           label: '个人部分-' + item.name
         });
       });
       
       // 添加合计列
       let postHeader = [
         { prop: 'compPartTotal', label: '单位合计' },
         { prop: 'personPartTotal', label: '个人合计' },
         { prop: 'insurancePartTotal', label: '社保合计' },
         { prop: 'housefundBasemoney', label: '公积金基数' },
         { prop: 'housefund-comppart', label: '公积金公司' },
         { prop: 'housefund-personpart', label: '公积金个人' },
         { prop: 'houseFundTotal', label: '公积金合计' },
         { prop: 'disableMoney', label: '残保金' },
         { prop: 'allPartTotal', label: '全部合计' }
       ];
       
       // 合并所有表头
       tableHeaders = tableHeaders.concat(preHeader).concat(compHeaders).concat(personHeaders).concat(postHeader);
       
       this.$confirm("此操作将导出外包保险数据到Excel文件，是否继续？", "提示", {
           confirmButtonText: "确定",
           cancelButtonText: "取消",
           type: "warning"
         }
       ).then(() => {
         // 准备导出数据
         const exportData = [];
         
         // 添加表头行
         const headerRow = tableHeaders.map(header => header.label);
         exportData.push(headerRow);
         
         // 添加数据行
         this.outsourceData.forEach(item => {
           const row = tableHeaders.map(header => {
             return item[header.prop] || '0.00';
           });
           exportData.push(row);
         });
         
         // 添加合计行
         if (this.outsourceData.length > 0) {
           const totalRow = ['合计', '', ''];
           
           // 从第4列开始计算合计（跳过姓名、部门、基数）
           for (let i = 3; i < tableHeaders.length; i++) {
             const columnProp = tableHeaders[i].prop;
             const columnTotal = this.outsourceData.reduce((sum, row) => 
               sum + parseFloat(row[columnProp] || 0), 0);
             totalRow[i] = columnTotal.toFixed(2);
           }
           
           exportData.push(totalRow);
         }
         
         // 调用通用导出函数
         this.exportToExcel(exportData, `${this.accountCode}_${this.currentMonth}_外包保险明细.xlsx`);
       });
     },

     uploadDetail () {
      if(this.outsourceData.length>0){
        this.$confirm('检测到已经存在当月外包数据，重新上传会覆盖已有数据，是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$emit('upload-detail',this.accountCode,-1,0,0)
        }).catch(() => {
          
        });
      }else{
        this.$emit('upload-detail',this.accountCode,-1,0,0)
      }
    },
    reUploadDetail(accountCode,sheetIndex,reUpload,isConfirm){
      this.$emit('upload-detail',accountCode,sheetIndex,reUpload,isConfirm)
    },

     addEmp(type){
      let accountCode = this.accountCode;
      let accountPeriod = this.currentMonth;
      this.$refs.empAdd.show(accountCode,accountPeriod,type);
     },
     showDoc(type){
        this.paraVisible2 = true;
        this.$nextTick(() => {
          let accountCode = this.accountCode;
        this.$refs.insuranceParameter.show(accountCode);
        })
     },

     adjustBase(type) {
      let accountCode = this.accountCode;
      this.adjustBaseType = type;
      // 取当前tab的数据
      let data = [];
      if (type === 'insurance') {
        data = this.insuranceData.map(item => ({
          ...item,
          adjustedSalary: ''
        }));
        this.$refs.insuranceAdjustBase.show(accountCode,this.currentMonth,data);
      } else if (type === 'housefund') {
        data = this.housefundData.map(item => ({
          ...item,
          adjustedSalary: ''
        }));
        this.$refs.housefundAdjustBase.show(accountCode,this.currentMonth,data);
      } else if (type === 'outsource') {
        data = this.outsourceData.map(item => ({
          ...item,
          insuranceAdjustedSalary: '',
          housefundAdjustedSalary: ''
        }));
        this.$refs.outsourceAdjustBase.show(accountCode,this.currentMonth,data);
      }
      // this.adjustBaseTableData = data;
      // this.adjustBaseDialogVisible = true;
     
    },

     getOutsourceDataList() {
      this.loading = true;
      return new Promise((resolve, reject) => {
        this.$http({
          url: this.$http.adornUrl(`/salary/outsource/listDetail/${this.accountCode}/${this.currentMonth}`),
          method: 'get'
        }).then(({data}) => {
          this.loading = false;
          if (data && data.code === 0) {
            this.outsourceData = data.mxdatalist.map(row => {
              this.$set(row, 'isEditing', false);
              return row;
            });
            this.outsourceItems = data.itemList;
            this.outsourceComppartItems = data.comppartList;
            this.outsourcePersonpartItems = data.personpartList;
            this.outsourceEditProp = data.itemCodes;
            this.outsourceId = data.outsourceId;
            this.$nextTick(() => {
              this.$forceUpdate();
            });
          } else {
            this.outsourceData = [];
            this.outsourceComppartItems = [];
            this.outsourcePersonpartItems = [];
            this.outsourceEditProp = [];
            this.outsourceId = '';
          }
          resolve();
        }).catch((error) => {
          console.error('获取外包数据失败:', error);
          this.loading = false;
          reject(error);
        });
      });
    },
      // 获取社保数据列表
      /**
       * 获取社保数据列表
       * 优化版：确保每行数据都有isEditing属性，且初始值为false
       * 加载后强制组件重新渲染，确保UI更新
       */
      getInsuranceDataList() {
        this.loading = true;
        this.$http({
          url: this.$http.adornUrl(`/salary/insurance/listDetail/${this.accountCode}/${this.currentMonth}`),
          method: 'get',
          params: this.$http.adornParams({
            
          })
        }).then(({data}) => {
          this.loading = false;
          if (data && data.code === 0) {
            this.insuranceEditProp = data.itemCodes
            this.insuranceId = data.insuranceId
            this.insuranceItems = data.itemList;
            this.insuranceComppartItems = data.comppartList;
            this.insurancePersonpartItems = data.personpartList;
            this.insuranceData = data.mxdatalist.map(row => {
              // 始终将isEditing设为false，确保每次打开都重置状态
              this.$set(row, 'isEditing', false);
              return row;
            });
            
            // 更新后强制重新渲染
            this.$nextTick(() => {
              this.$forceUpdate();
            });
          }
        }).catch(() => {
          this.loading = false;
        })
      },
     
      // 获取公积金数据列表
      getHousefundDataList () {
          this.loading = true;
          this.$http({
        url: this.$http.adornUrl(`/salary/housefund/listDetail/${this.accountCode}/${this.currentMonth}`),
            method: 'get',
            params: this.$http.adornParams({
          
            })
          }).then(({data}) => {
            this.loading = false;
            if (data && data.code === 0) {
            this.housefundComppartItems = data.comppartList
            this.housefundPersonpartItems = data.personpartList
            this.housefundEditProp = data.itemCodes
            this.housefundId = data.housefundId
            this.housefundItems= data.itemList;
            this.housefundData = data.mxdatalist.map(row => {
            // 始终将isEditing设为false，确保每次打开都重置状态
            this.$set(row, 'isEditing', false);
            return row;
          });
          
          // 更新后强制重新渲染
          this.$nextTick(() => {
            this.$forceUpdate();
          });
        }
      }).catch(() => {
            this.loading = false;
      })
    },
        // 获取工资单据列表
        getPayrollDataList () {
        this.loading = true;
        this.$http({
          url: this.$http.adornUrl(`/salary/payroll/listDetail/${this.accountCode}/${this.currentMonth}`),
          method: 'get'
        }).then(({data}) => {
          this.loading = false;
          if (data && data.code === 0) {
            this.payrollItems = data.itemList;
            this.payrollEditProp = data.itemCodes;
            this.payrollId = data.payrollId;
            // 初始化数据并确保每行都有isEditing属性
            this.payrollData = data.mxDataList.map(row => {
              // 始终将isEditing设为false，确保每次打开都重置状态
              this.$set(row, 'isEditing', false);
              return row;
            });
            
            // 初始化个税回执状态
            this.isTaxReceiptUploaded = data.taxReceiptUploaded === true || data.taxReceiptUploaded === '1';
          
          } else {
            this.payrollData = [];
            this.payrollItems = [];
            this.payrollEditProp = [];
            this.payrollId = '';
            this.isTaxReceiptUploaded = false;
          }
          
          // 强制确保所有表格相关方法都被正确初始化
          this.$nextTick(() => {
            // 确保Vue实例重新渲染并绑定所有方法
            this.$forceUpdate();
          });
        }).catch((error) => {
          console.error('获取工资单数据失败:', error);
          this.loading = false;
        });
      },
      handleLeave(row,type){
        // 只处理工资单的逻辑
        if(type === 'payroll'){
          let hasInsuranceData = false;
          if(this.payrollItems && this.payrollItems.length > 0){
            for(let i = 0; i < this.payrollItems.length; i++){
              const column = this.payrollItems[i];
              if(this.isInsuranceColumn(column)){
                const val = parseFloat(row[column.code] || 0);
                if(val !== 0){
                  hasInsuranceData = true;
                  break;
                }
              }
            }
          }
          if(hasInsuranceData){
            this.$message.warning('该职员存在保险数据，不可离职。请先删除相关保险数据后再操作离职。');
            return;
          }
        }
        let alertMessage = "请确认是否将该职员离职，并且删除本月工资数据?";
        this.$confirm(alertMessage, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // 发起HTTP请求
          this.$http({
            url: this.$http.adornUrl(`/salary/salarystatus/handleLeave/${this.accountCode}/${this.currentMonth}/${row.empdocid}`),
            method: 'put',
     
          }).then(({ data }) => {
            // 处理响应
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  if(type=='payroll'){
                    this.getPayrollDataList(); // 刷新数据列表
                  }else if(type==='housefund'){
                    this.getHousefundDataList();
                  }else if(type==='insurance'){
                    this.getInsuranceDataList();
                  }else if(type==='outsource'){
                    this.getOutsourceDataList();
                  }
                 
                }
              });
            } else {
              // 操作失败
              this.$message.error(data.msg);
            }
          });
        }).catch(() => {
          
        });
      },
      handleDelete(row, type) {
        let alertMessage = "请确认是否将当前职员删除?";
        this.$confirm(alertMessage, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // 根据type拼接url
          let url = '';
          if (type === 'payroll') {
            url = `/salary/payroll/handleDelete/${this.accountCode}/${this.currentMonth}/${row.empdocid}`;
          } else if (type === 'housefund') {
            url = `/salary/housefund/handleDelete/${this.accountCode}/${this.currentMonth}/${row.empdocid}`;
          } else if (type === 'insurance') {
            url = `/salary/insurance/handleDelete/${this.accountCode}/${this.currentMonth}/${row.empdocid}`;
          } else if (type === 'outsource') {
            url = `/salary/outsource/handleDelete/${this.accountCode}/${this.currentMonth}/${row.empdocid}`;
          } else {
            this.$message.error('未知类型，无法删除');
            return;
          }

          this.$http({
            url: this.$http.adornUrl(url),
            method: 'delete',
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  // 根据type刷新对应数据
                  if (type === 'payroll') {
                    this.getPayrollDataList();
                  } else if (type === 'housefund') {
                    this.getHousefundDataList();
                  } else if (type === 'insurance') {
                    this.getInsuranceDataList();
                  } else if (type === 'outsource') {
                    this.getOutsourceDataList();
                  }
                }
              });
            } else {
              this.$message.error(data.msg);
            }
          });
        }).catch(() => {});
      },
      
      frontGenerate(type){
        
        let alertMessage = "重新生成将覆盖当前已有数据，请确认是否执行此操作?";
        let url ='';
        if(type==='payroll'){
            url = `/salary/payroll/generatePayroll/${this.accountCode}/${this.currentMonth}`;
          }else if(type==='housefund'){
            url = `/salary/housefund/generateHousefund/${this.accountCode}/${this.currentMonth}`;
          }else if(type==='insurance'){
            url = `/salary/insurance/generateInsurance/${this.accountCode}/${this.currentMonth}`;
          }else if(type==='outsource'){
            url = `/salary/outsource/generateOutsource/${this.accountCode}/${this.currentMonth}`;
          }
        this.$confirm(alertMessage, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // 发起HTTP请求
          this.$http({
            url: this.$http.adornUrl(url),
            method: 'get',
     
          }).then(({ data }) => {
            // 处理响应
            if (data && data.code === 0) {
              // 操作成功
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  if(type==='payroll'){
                    this.isPayrollConfirmed = false; // 重置确认状态
                    this.getPayrollDataList(); // 刷新数据列表
                  }else if(type==='housefund'){
                    this.isHousefundConfirmed =false;
                    this.getHousefundDataList();
                  }else if(type==='insurance'){
                    this.isInsuranceConfirmed =false;
                    this.getInsuranceDataList();
                  }else if(type==='outsource'){
                    this.isOutsourceConfirmed = false;
                    this.getOutsourceDataList();
                  }
                 
                }
              });
            } else {
              // 操作失败
              this.$message.error(data.msg);
            }
          });
        }).catch(() => {
          
        });
      },

      outsourceSave(){
        this.outsourceEditFlag = false;

        let rows=this.outsourceData;
        let data ={};
        data.rows = rows;
        data.itemNames=this.outsourceItems;
        const newArr=rows.map(item=>item.empdocid);
        const isRepeat=newArr.some((x,index,rows)=>rows.findIndex(y=>y==x)!=index);
        if(isRepeat){
          this.$message.error('当前公积金单据中存在重复人员，不允许保存，请检查后重试！')
          return;
        }
     
        let totalmoney=0;
	      let persontotalmoney=0;
          for(let i=0;i<rows.length;i++){
          if(!checkEm(rows[i].allPartTotal)){
            totalmoney += parseFloat(rows[i].allPartTotal);
          }
          if(!checkEm(rows[i].personPartTotal)){
            persontotalmoney += parseFloat(rows[i].personPartTotal);
          }
          if(!checkEm(rows[i]['housefund-personpart'])){
            persontotalmoney += parseFloat(rows[i]['housefund-personpart']);
          }
        }
        let createdate = moment(this.currentMonth).endOf("month").format("YYYY-MM-DD");
        this.$http({
              url: this.$http.adornUrl(`/salary/outsource/${!this.outsourceId ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.outsourceId || undefined,
                'accountCode':this.accountCode,
                'insurancemonth': this.currentMonth,
                'empCount': rows.length,
                'createdate': createdate,
                'totalmoney': totalmoney.toFixed(2),
                'persontotalmoney': persontotalmoney.toFixed(2),
                'itemNames': JSON.stringify(this.outsourceItems),
                'caloutsourcemx': JSON.stringify(data),
                'caloutsourcemxdata': JSON.stringify(rows)
              })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
              
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
      },

      handleClick(tab) {
        if (tab.name === 'first') {
          this.getInsuranceDataList();
        } else if (tab.name === 'second') {
          this.getHousefundDataList();
        } else if (tab.name === 'third') {
           // 先查社保、公积金、外包保险，再查工资单
           Promise.all([
            this.getInsuranceDataList(),
            this.getHousefundDataList(),
            this.getOutsourceDataList()
          ]).then(() => {
            this.getPayrollDataList();
          });
        } else if (tab.name === 'fourth') {
          this.getOutsourceDataList();
        }
      },
      payrollSelectionChangeHandle (val) {
        this.payrollDataListSelections = val
      },
     // 多选
     insuraceSelectionChangeHandle (val) {
        this.insuranceDataListSelections = val
      },
       // 多选
     housefundSelectionChangeHandle (val) {
        this.housefundDataListSelections = val
      },
    

      insuranceDelete (type) {
        
        let url ='';
        if(type==='payroll'){
            url = `/salary/payroll/delete`;
          }else if(type==='housefund'){
            url = `/salary/housefund/delete`;
          }else if(type==='insurance'){
            url = `/salary/insurance/delete`;
          }else if(type==='outsource'){
            url = `/salary/outsource/delete`;
          }

        this.$confirm(`确定对当前数据进行删除操作?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http({
            url: this.$http.adornUrl(url),
            method: 'delete',
            params: this.$http.adornParams({
              'accountCode': this.accountCode,
            'accountPeriod':this.currentMonth,
            })
          }).then(({data}) => {
            if (data.code == 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  
                  if(type==='payroll'){
                    this.getPayrollDataList()
                  }else if(type==='housefund'){
                    this.getHousefundDataList()
                  }else if(type==='insurance'){
                    this.getInsuranceDataList()
                  }else if(type==='outsource'){
                    this.getOutsourceDataList()
                  }
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        })
      },

    
    
 
 

    

    housefundHandleMouseEnter(row, column, cell, event) {
      const property = column.property
      this.commonHandleMouseEnter(row, column, cell, event, 'housefund')
      if (this.housefundEditFlag && this.housefundEditProp.includes(property)) {
        if(cell.querySelector('.item__txt')) {
          cell.querySelector('.item__txt').classList.add('item__txt--hover')
        }
      }
    },
    housefundHandleMouseLeave(row, column, cell, event) {
      const property = column.property
      let accuracy = 0;
      this.housefundItems.forEach(item => {
        if(item.insuranceitemscode==property){
            accuracy = item.accuracy || 0;
        }
      })

      this.commonHandleMouseLeave(row, column, cell, event, 'housefund')
      if (this.housefundEditProp.includes(property)) {
        // if(cell.querySelector('.item__txt')) {
        //   cell.querySelector('.item__txt').classList.remove('item__txt--hover')
        // }
        //this.cancelEditable(cell, row, column, this.housefundEditProp, accuracy)
      }   
    },
    
  
    insuranceHandleMouseEnter(row, column, cell, event) {
     // debugger
      const property = column.property
      this.commonHandleMouseEnter(row, column, cell, event,'insurance')
      if (this.insuranceEditFlag && this.insuranceEditProp.includes(property)) {
        cell.querySelector('.item__txt').classList.add('item__txt--hover')
      }

  
    },
    insuranceHandleMouseLeave(row, column, cell, event) {
      const property = column.property
      let accuracy = 0;
      this.insuranceItems.forEach(item => {
        if(item.insuranceitemscode==property){
            accuracy = item.accuracy || 0;
        }
      })

      this.commonHandleMouseLeave(row, column, cell, event,'insurance')
      if (this.insuranceEditProp.includes(property)) {
          //cell.querySelector('.item__txt').classList.remove('item__txt--hover')
         // this.cancelEditable(cell,row,column,this.insuranceEditProp,accuracy)
      }   
    },
    cancelEditable (cell,row,column) {
      cell.querySelector('.item__txt').style.display = 'block'
      cell.querySelector('.item__input').style.display = 'none'
    },
  commonHandleCellClick(row, column, cell, event,type){
    cell.querySelector('.item__txt').style.display = 'none'
      cell.querySelector('.item__input').style.display = 'block'
      cell.querySelector('input').focus()
  },
  commonHandleMouseEnter(row, column, cell, event,type) {
    const property = column.property
      if(type!='outsource' && (property.indexOf('-comppart')!=-1 || property.indexOf('-personpart')!=-1)){
        const { id } = row
        const tooltipDom = document.createElement('div')
        tooltipDom.style.cssText = `
            display: inline-block;
            max-width: 400px;
            max-height: 400px;
            position: absolute;
            top: ${event.clientY + 5}px;
            left: ${event.clientX}px;
            padding:5px 10px;
            overflow: auto;
            font-size: 12px;
            color: #595959;
            background: #fff;
            border-radius: 5px;
            z-index: 19999;
            box-shadow: 0 4px 12px 1px #ccc;    
        `    
        let calmethod = row[property+'_calmethod'];
        let formula = '';
        if(calmethod =='0'){
          formula = row[property+'_basemoney']+'*'+row[property+'_rate']+'%='+row[property]
        }else{
          formula = '固定金额：'+row[property];
        }
        let str = column.label+'计算公式：'+formula
        tooltipDom.innerHTML = str
        tooltipDom.setAttribute('id', `tooltip-${id}`)
        document.body.appendChild(tooltipDom)
      }
      
  },
  commonHandleMouseLeave(row, column, cell, event,type) {
    
    const property = column.property
      if(type!='outsource' && (property.indexOf('-comppart')!=-1 || property.indexOf('-personpart')!=-1)){
        const { id } = row
        const tooltipDomLeave = document.querySelectorAll(`#tooltip-${id}`)
        if (tooltipDomLeave.length) {
            tooltipDomLeave.forEach(dom => {
                document.body.removeChild(dom)
            })
        } 
      }
  },
    /** 取消编辑状态 */
    cancelEditable (cell,row,column,itemProp,accuracy) {
      debugger
      const property = column.property
      let value = (parseFloat(row[property] || 0)).toFixed(accuracy);
      this.$set(row,property,value);

      cell.querySelector('.item__txt').style.display = 'block'
      cell.querySelector('.item__input').style.display = 'none'
      //取消编辑，重算当前行的个人合计，单位合计，合计数据
      let compparttotal = 0;
      let personparttotal = 0;
      let totalparttotal = 0;

      //遍历row 中的数据，计算个人合计，单位合计，合计数据
      //如果insuranceitemscode
      for(let i in itemProp){
        if(itemProp[i].indexOf('comppart') !=-1){
            let comppart = row[itemProp[i]] || 0;
            compparttotal = compparttotal + parseFloat(comppart);
            totalparttotal = totalparttotal + parseFloat(comppart);
          }else if(itemProp[i].indexOf('personpart') !=-1){
            let personpart = row[itemProp[i]] || 0;
            personparttotal = personparttotal + parseFloat(personpart);
            totalparttotal = totalparttotal + parseFloat(personpart);
          }

        }

        row.compparttotal = compparttotal.toFixed(accuracy);
        row.personparttotal = personparttotal.toFixed(accuracy);
        row.totalparttotal = totalparttotal.toFixed(accuracy);
    },
        /** 取消编辑状态 */
    cancelOutsourceEditable (cell,row,column,itemProp,accuracy) {
      debugger
      const property = column.property
      let value = (parseFloat(row[property] || 0)).toFixed(accuracy);
      this.$set(row,property,value);

      cell.querySelector('.item__txt').style.display = 'block'
      cell.querySelector('.item__input').style.display = 'none'
      //取消编辑，重算当前行的个人合计，单位合计，合计数据
      let compparttotal = 0;
      let personparttotal = 0;
      let totalparttotal = 0;
      let housefundtotal =0;
  

      let allparttotal = 0;

      //遍历row 中的数据，计算个人合计，单位合计，合计数据
      //如果insuranceitemscode
      for(let i in itemProp){
        if(itemProp[i].indexOf('-comppart') !=-1 || itemProp[i].indexOf('-personpart') !=-1){
          //
          if(itemProp[i] =='housefund-comppart' || itemProp[i] =='housefund-personpart'){
            let money = row[itemProp[i]] || 0;
            housefundtotal = housefundtotal  + parseFloat(money);
            allparttotal = allparttotal + parseFloat(money);
          }else{
          
            if(itemProp[i].indexOf('-comppart') !=-1){
              let comppart = row[itemProp[i]] || 0;
              compparttotal = compparttotal + parseFloat(comppart);
              totalparttotal = totalparttotal + parseFloat(comppart);
              allparttotal = allparttotal + parseFloat(comppart);

            }else if(itemProp[i].indexOf('-personpart') !=-1){
              let personpart = row[itemProp[i]] || 0;
              personparttotal = personparttotal + parseFloat(personpart);
              totalparttotal = totalparttotal + parseFloat(personpart);
              allparttotal = allparttotal + parseFloat(personpart);
            }
          }

        }else if(itemProp[i] == 'disableMoney'){
          let money = row[itemProp[i]] || 0;
          allparttotal = allparttotal + parseFloat(money);
        }
        

        

        }

        row.compPartTotal = compparttotal.toFixed(accuracy);
        row.personPartTotal = personparttotal.toFixed(accuracy);
        row.insurancePartTotal = totalparttotal.toFixed(accuracy);

        row.houseFundTotal = housefundtotal.toFixed(accuracy);
        row.allPartTotal = allparttotal.toFixed(accuracy);
    },

      getSummaries(param){
          const { columns, data } = param;
          const sums = [];
          columns.forEach((column, index) => {
              if (index == 2) {
                  sums[index] = '合计';
              }
              const values = data.map(item => Number(item[column.property]));
              if (column.property && !values.every(value => isNaN(value)) && (
                column.property.indexOf('comppart')!=-1 ||
                column.property.indexOf('personpart')!=-1 ||
                column.property.indexOf('basemoney')!=-1 ||
                column.property.indexOf('total')!=-1 ||
                column.property.indexOf('salaryitemscode_')!=-1 ||
                column.property.indexOf('Total')!=-1
              )) {
                  sums[index] = this.formatNumber(values.reduce((prev, curr) => {
                      const value = Number(curr);
                      if (!isNaN(value)) {
                          return (Number(prev) + Number(curr)).toFixed(2);
                      } else {
                          return prev;
                      }
                  }, 0));
              }
          });
          
       return sums;
      },

    calculateHousefundTotalAmount() {
      if (!this.housefundData || this.housefundData.length === 0) return 0;
      
      let total = 0;
      this.housefundData.forEach(item => {
        if (item.totalparttotal) {
          total += parseFloat(item.totalparttotal);
        }
      });
      
      return total;
    },
    cancelHousefundEditable(cell, row, column, itemProp, accuracy) {
      const property = column.property
      let value = (parseFloat(row[property] || 0)).toFixed(accuracy);
      this.$set(row, property, value);
      
      if(cell.querySelector('.item__txt') && cell.querySelector('.item__input')) {
        cell.querySelector('.item__txt').style.display = 'block'
        cell.querySelector('.item__input').style.display = 'none'
      }
      
      //计算公积金合计数据
      this.handleHousefundCalculation(row);
    },

    handleHousefundCalculation(row) {
      // 计算单行公积金数据的合计值
      let compparttotal = 0;
      let personparttotal = 0;
      let totalparttotal = 0;
      
      try {
        // 计算单位部分合计
        if (this.housefundComppartItems && this.housefundComppartItems.length > 0) {
          this.housefundComppartItems.forEach(item => {
            if (!item.insuranceitemscode) return;
            // 确保数值是有效的数字
            const valueStr = row[item.insuranceitemscode];
            const value = parseFloat(valueStr || 0);
            if (!isNaN(value)) {
              compparttotal += value;
              totalparttotal += value;
            }
          });
        }
        
        // 计算个人部分合计
        if (this.housefundPersonpartItems && this.housefundPersonpartItems.length > 0) {
          this.housefundPersonpartItems.forEach(item => {
            if (!item.insuranceitemscode) return;
            // 确保数值是有效的数字
            const valueStr = row[item.insuranceitemscode];
            const value = parseFloat(valueStr || 0);
            if (!isNaN(value)) {
              personparttotal += value;
              totalparttotal += value;
            }
          });
        }
        
        // 设置合计值（使用$set确保响应式）
        this.$set(row, 'compparttotal', compparttotal.toFixed(2));
        this.$set(row, 'personparttotal', personparttotal.toFixed(2));
        this.$set(row, 'totalparttotal', totalparttotal.toFixed(2));
      } catch (error) {
        console.error('Error calculating housefund totals:', error);
      }
    },
    // 计算工资单总金额
    calculatePayrollTotalAmount() {
      if (!this.payrollData || this.payrollData.length === 0) return 0;
      
      let total = 0;
      // 寻找应付工资字段
      let shouldPayCode = '';
      if (this.payrollItems && this.payrollItems.length > 0) {
        const shouldPayItem = this.payrollItems.find(item => item.name === '应付工资');
        if (shouldPayItem) {
          shouldPayCode = shouldPayItem.code;
        }
      }
      
      // 计算总金额
      this.payrollData.forEach(item => {
        if (shouldPayCode && item[shouldPayCode]) {
          // 优先使用应付工资
          total += parseFloat(item[shouldPayCode] || 0);
        } else if (item.salaryitemscode_99) {
          // 备选使用总计字段
          total += parseFloat(item.salaryitemscode_99 || 0);
        }
      });
      
      return total;
    },

    // 新增工资单行数据保存方法
    savePayrollRowToBackend(row) {
      // 显示加载状态
          this.loading = true;
      
      // 发送请求到后台保存修改后的数据
          this.$http({
        url: this.$http.adornUrl('/salary/payroll/updateSingleRow'),
        method: 'post',
        data: this.$http.adornData({
          'id': this.payrollId || undefined,
              'accountCode': this.accountCode,
              'payrollmonth': this.currentMonth,
          'itemNames': JSON.stringify(this.payrollItems),
          'payrollmxdata': JSON.stringify(row)
            })
          }).then(({data}) => {
            this.loading = false;
            if (data && data.code === 0) {
          this.$message({
            message: '保存成功',
            type: 'success',
            duration: 1500
          });
          // 刷新数据
          this.getPayrollDataList();
            } else {
          this.$message.error(data.msg || '保存失败');
            }
      }).catch((error) => {
            this.loading = false;
        console.error('Error saving payroll row:', error);
        this.$message.error('网络错误，请稍后重试');
      });
    },
 
    async submitAdjustBase() {
      // 可批量请求，也可循环单个请求
      const type = this.adjustBaseType;
      const updateList = this.adjustBaseTableData.filter(row => row.adjustedSalary != row.basemoney && row.adjustedSalary != row.insuranceBasemoney);

      if (updateList.length === 0) {
        this.$message.warning('没有需要调整的数据');
        return;
      }

      try {
        for (let row of updateList) {
          let url = '';
          let data = {};
          if (type === 'insurance') {
            url = '/salary/insurance/updateBaseMoney';
            data = {
              empdocid: row.empdocid,
              accountCode: this.accountCode,
              accountPeriod: this.currentMonth,
              basemoney: row.adjustedSalary
            };
          } else if (type === 'housefund') {
            url = '/salary/housefund/updateBaseMoney';
            data = {
              empdocid: row.empdocid,
              accountCode: this.accountCode,
              accountPeriod: this.currentMonth,
              basemoney: row.adjustedSalary
            };
          } else if (type === 'outsource') {
            url = '/salary/outsource/updateBaseMoney';
            data = {
              empdocid: row.empdocid,
              accountCode: this.accountCode,
              accountPeriod: this.currentMonth,
              insuranceBasemoney: row.adjustedSalary
            };
          }
          await this.$http({
            url: this.$http.adornUrl(url),
            method: 'post',
            data: this.$http.adornData(data)
          });
        }
        this.$message.success('调整成功');
        this.adjustBaseDialogVisible = false;
        // 刷新数据
        if (type === 'insurance') this.getInsuranceDataList();
        if (type === 'housefund') this.getHousefundDataList();
        if (type === 'outsource') this.getOutsourceDataList();
      } catch (e) {
        this.$message.error('调整失败');
      }
    },
    handleMessage (message) {
      debugger
        if (message.type === 'salary') {
          //alert(message.content);
          debugger
          let message1 = message.content;
          let messageArr = message1.split(';');
          let messageCode = messageArr[0];
          let messageContent = messageArr[1];
          if(messageCode=='confirm'){//确认sheet名称
              this.$refs.templateSelect.show(this.accountCode,messageContent);
          }else if(messageCode=='confirmEmployee'){//确认新增  减少的员工弹框
            //此时会携带员工信息，需要解析
            this.$refs.empConfirm.show(this.accountCode,messageContent);
          }else if(messageCode=='addTemplate'){
            alert("addTemplate")
            this.$refs.templateSelect.show(this.accountCode,messageContent);
            //this.$refs.addTemplate.show(this.accountCode);
          }else if(messageCode=='updateTemplate'){
            alert("updateTemplate")
            this.$refs.addTemplate.show(this.accountCode);
          }
          else{
            this.$message({
              message: messageContent,
              type: messageCode,
              duration: 1500,
              onClose: () => {
                this.getOutsourceDataList()
                this.getPayrollDataList()
              }
            })
          }
       
          
        }
      },
      // 判断个税是否上传
    isPersonalTaxUploaded(row) {
      return row.hasOwnProperty('salaryitemscode_82') && row['salaryitemscode_82'] !== '' && row['salaryitemscode_82'] != null;
    },




    // 选择Excel文件
    selectExcelFile() {
      return new Promise((resolve) => {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.xlsx,.xls';
        input.onchange = (e) => {
          resolve(e.target.files[0]);
        };
        input.click();
      });
    },

    showTemp(){
      this.$refs.addTemplate.show(this.accountCode, null);
    },

    async uploadDetail2(sheetId,sheetIndex,step) {
      try {
        // 1. 选择文件
        //如果有step了，就不要选 择文件
        const file = step ? null : await this.selectExcelFile();
      
        // 2. 构造FormData上传
        const formData = new FormData();
        formData.append('file', file);
        formData.append('accountCode', this.accountCode);
        formData.append('currentMonth', this.currentMonth);
        //如果传递了randomId，则设置
        if (sheetId) {
          formData.append('randomId', sheetId);
        }
        //如果传递了sheetIndex，则设置
        if (sheetIndex) {
          formData.append('sheetIndex', sheetIndex);
        }
        //如果传递了step，则设置
        let alertMessage = '文件开始上传，请稍候...';
        if (step) {
          formData.append('confirmStep', step);
          alertMessage = '文件继续上传中，请稍候...';
        }

        // 3. 上传到后端
       // this.loading = true;
        this.$message.info(alertMessage);
        const { data } = await this.$http({
          url: this.$http.adornUrl('/salary/outsource/uploadTempExcel'),
          method: 'post',
          data: formData,
          headers: { 'Content-Type': 'multipart/form-data' }
        });

        this.loading = false;

        if (!data || data.code !== 0) {
          this.$message.error(data.msg || '上传失败');
          return;
        }

        // 4. 根据返回类型处理不同的情况
        switch (data.type) {
          case 'addTemplate':
            // 未检测到模板，提示是否构建
            this.$confirm(data.msg, '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              // 用户确认创建模板
              this.$refs.addTemplate.show(this.accountCode, data.randomId);
            }).catch(() => {
              this.$message.warning('必须设置模板才能继续导入');
            });
            break;

          case 'selectSheet':
            // 多个工作表，显示选择对话框
            this.sheetNames = data.sheets;
            this.selectedSheetName = '';
            this.sheetSelectDialogVisible = true;
            this.$refs.templateSelect.show(this.accountCode, data.randomId,data.sheets);
            break;

          case 'editTemplate':
            // 模板不匹配，提示更新模板

             this.$confirm(data.msg, '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(async() => {
              // 用户确认创建模板
              const res = await this.$http({
                url: this.$http.adornUrl('/salary/outsource/reBuildTemplate'),
                method: 'post',
                data: this.$http.adornData({
                  accountCode: this.accountCode,
                  randomId: data.randomId
                })
              });
              if (res.data && res.data.code === 0) {
                 this.$refs.addTemplate.show(this.accountCode, data.randomId);
              } else {
                this.$message.error(res.data.msg || '模板更新失败');
                return;
              }
             
            }).catch(() => {
              this.$message.warning('必须更新模板后才能继续导入');
            });


            // this.$alert(data.msg, '提示', {
            //   confirmButtonText: '去更新',
            //   type: 'warning'
            // }).then(() => {
            //   this.$refs.addTemplate.show(this.accountCode, data.randomId);
            // });
            break;

          case 'confirmEmployee':
            // 开始构建模板
    
            this.$refs.empConfirm.show(this.accountCode, data.randomId,data.sheetIndex,data.addList,data.reduceList);
            break;
          case 'success':   
          this.$message.success(data.msg || '导入成功');
            this.getOutsourceDataList();
            break;
          default:
            // 其他情况，可能是导入成功
            this.$message.success(data.msg || '导入成功');
            this.getOutsourceDataList();
        }
      } catch (error) {
        this.loading = false;
        console.error('上传处理失败:', error);
        this.$message.error('上传处理失败，请稍后重试');
      }
    },



    handleContinueUpload(randomId,sheetIndex,step) {
      this.uploadDetail2(randomId,sheetIndex,step);
    },

    // async handleContinueUpload(randomId) {
    //   // 继续上传，带上 randomId
    //   try {
    //     this.loading = true;
    //     const { data } = await this.$http({
    //       url: this.$http.adornUrl('/salary/outsource/uploadTempExcel'),
    //       method: 'post',
    //       data: this.$http.adornData({
    //         randomId,
    //         accountCode: this.accountCode,
    //         currentMonth: this.currentMonth,
    //         confirmStep: 'TEMPLATE_CHECK',
    //         confirmResult: 'true'
    //       })
    //     });
    //     this.loading = false;
    //     if (!data || data.code !== 0) {
    //       this.$message.error(data.msg || '上传失败');
    //       return;
    //     }
    //     // 处理后端返回
    //     switch (data.type) {
    //       case 'buildTemplate':
    //         this.$message.success(data.msg || '请设置模板配置');
    //         break;
    //       default:
    //         this.$message.success(data.msg || '导入成功');
    //         this.getOutsourceDataList();
    //     }
    //   } catch (e) {
    //     this.loading = false;
    //     this.$message.error('上传失败');
    //   }
    // },

    formatNumber(value) {
      return formatNumber(value)
    },
    },
    mounted () {
      try {
       
        socket.setUserId(this.$store.state.user.name)
        socket.connect() // 连接 WebSocket
        // 监听消息接收
        socket.onSalaryMsgReceived = (message) => {
          debugger
          this.handleMessage(message)
        }
        
        // 初始化所有表格数据
        // this.$nextTick(() => {
        //   if (this.activeName === 'first' && this.insuranceData && this.insuranceData.length > 0) {
        //     this.insuranceData = this.insuranceData.map(row => {
        //       if (row.isEditing === undefined) {
        //         this.$set(row, 'isEditing', false);
        //       }
        //       return row;
        //     });
        //   }
          
        //   // 确保所有必要的方法都被定义
        //   this.$forceUpdate();
        // });
      } catch (mountError) {
        console.error('Error in mounted hook:', mountError);
      }
    },
  computed: {
    declareTableData() {
      debugger
      // 1. 获取工资单"应付工资"字段名
      let payCol = '';
      if (this.payrollItems && this.payrollItems.length > 0) {
        const found = this.payrollItems.find(item => item.name === '应付工资');
        if (found) payCol = found.code;
      }
      // 2. 获取社保单"个人部分"下的所有列名
      const personCols = (this.insurancePersonpartItems || []).map(item => ({
        code: item.insuranceitemscode,
        name: item.insuranceitemsname
      }));
      // 3. 获取公积金单"个人部分"下的所有列名
      const housefundPersonCols = (this.housefundPersonpartItems || []).map(item => ({
        code: item.insuranceitemscode,
        name: item.insuranceitemsname
      }));
       // 3. 获取外包保险单"个人部分"下的所有列名
      const outsourcePersonCols = (this.outsourcePersonpartItems || []).map(item => ({
        code: item.code,
        name: item.name
      }));
      //增加 housefund-personpart
      outsourcePersonCols.push({ code: 'housefund-personpart', name: '公积金个人' });
      // 4. 组装数据
      return (this.payrollData || [])
        .filter(row => row && typeof row === 'object')
        .map(row => {
          // 工号、姓名、证件号
          const empdocid = row.empdocid || '';
          const empdoc_name = row.empdoc_name || '';
          const idcard = row.cardNo || '';
          const cardType = row.cardType || '';
          // 本期收入
          const income = payCol ? row[payCol] : '';
          // 社保单中该员工数据
          const insuranceRow = (this.insuranceData || []).find(r => r.empdocid === empdocid);
          // 公积金单中该员工数据
          const housefundRow = (this.housefundData || []).find(r => r.empdocid === empdocid);

          const outsourceRow = (this.outsourceData || []).find(r => r.empdocid === empdocid);
          // 基本养老保险费
          let yanglao = '';
          // 失业保险费
          let shiye = '';
          // 基本医疗保险费
          let yiliao = '';
          if (insuranceRow) {
            // 找到"基本养老"
            const yanglaoCol = personCols.find(c => c.name.indexOf('养老') !== -1);
            if (yanglaoCol) yanglao = insuranceRow[yanglaoCol.code] || '';
            // 找到"失业"
            const shiyeCol = personCols.find(c => c.name.indexOf('失业') !== -1);
            if (shiyeCol) shiye = insuranceRow[shiyeCol.code] || '';
            // 其余个人部分合计为"基本医疗保险费"
            yiliao = personCols
              .filter(c => c.name.indexOf('养老') === -1 && c.name.indexOf('失业') === -1)
              .reduce((sum, c) => {
                const v = parseFloat(insuranceRow[c.code] || 0);
                return sum + (isNaN(v) ? 0 : v);
              }, 0);
            yiliao = yiliao ? yiliao.toFixed(2) : '';
          }else{//找外包
              if (outsourceRow) {
                // 找到"基本养老"
                const yanglaoCol = outsourcePersonCols.find(c => c.name.indexOf('养老') !== -1);
                if (yanglaoCol) yanglao = outsourceRow[yanglaoCol.code] || '';
                // 找到"失业"
                const shiyeCol = outsourcePersonCols.find(c => c.name.indexOf('失业') !== -1);
                if (shiyeCol) shiye = outsourceRow[shiyeCol.code] || '';
                // 其余个人部分合计为"基本医疗保险费"
                yiliao = outsourcePersonCols
                  .filter(c => c.name.indexOf('养老') === -1 && c.name.indexOf('失业') === -1 && c.name.indexOf('公积金') === -1)
                  .reduce((sum, c) => {
                    const v = parseFloat(outsourceRow[c.code] || 0);
                    return sum + (isNaN(v) ? 0 : v);
                  }, 0);
                yiliao = yiliao ? yiliao.toFixed(2) : '';
              }
          }
          // 公积金
          let housefund = '';
          if (housefundRow) {
            // 找到"公积金"
            const gjjCol = housefundPersonCols.find(c => c.name.indexOf('公积金') !== -1);
            if (gjjCol) housefund = housefundRow[gjjCol.code] || '';
          }else{
             if (outsourceRow) {
                // 找到"公积金"
                const gjjCol = outsourcePersonCols.find(c => c.name.indexOf('公积金') !== -1);
                if (gjjCol) housefund = outsourceRow[gjjCol.code] || '';
              }
          }
          return {
            empdocid,
            empdoc_name,
            cardType,
            idcard,
            '本期收入': income,
            '基本养老保险费': yanglao,
            '失业保险费': shiye,
            '基本医疗保险费': yiliao,
            '住房公积金': housefund
          };
        });
    },
    
  },

  }
</script>
  
  <style lang='scss' scoped>

  .el-dialog__header {  
    padding: 0px;  
  }

  .el-dialog__body {  
    padding: 1px;  
  }

  .operation{
    padding: 10px;
    width: 100%;
  }

  .title_label{
    font-size: 16px;
    padding: 10px;
    text-align: center;
    color: #303133;
  }

  .choose-invoice-table-elipsis{
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis !important;
  }
  
  .custom-input {
    font-size: 12px;
  }

  /* 顶部和底部样式 */
  .top-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }
  
  .action-buttons {
    display: flex;
    align-items: center;
  }

  .total-amount {
    font-size: 14px;
    font-weight: normal;
  }
  
  /* 金额高亮样式 */
  .amount-value {
    color: #333333;
    font-weight: bold;
    font-size: 18px;
  }

  .confirm-status-disabled {
    display: inline-flex;
    align-items: center;
    color: #909399;
    font-size: 14px;
    cursor: not-allowed;
    margin-left: 10px;
  }
  /* 红色待确认样式 */
  .confirm-status {
    display: inline-flex;
    align-items: center;
    color: #F56C6C;
    font-size: 14px;
    cursor: pointer;
    margin-left: 10px;
  }
  
  .confirm-status i {
    margin-right: 5px;
    font-size: 16px;
  }
  
  /* 按钮样式 */
  .btn-confirm {
    background-color: #409EFF;
    color: white;
    border-color: #409EFF;
  }
  
  .btn-white {
    background-color: white;
    color: #606266;
    border-color: #DCDFE6;
  }
  
  .btn-gray {
    background-color: #909399;
    color: white;
    border-color: #909399;
  }
  
  .btn-yellow {
    background-color: #E6A23C;
    color: white;
    border-color: #E6A23C;
  }

  .btn-warning {
    background-color: #E6A23C;
    color: white;
    border-color: #E6A23C;
  }

  .invoice_class {
    min-width: 1200px;
  }

  .bottom-bar {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }
  

  
  .title_label {
    font-size: 16px;
    font-weight: bold;
  }

  .item {
    display: flex;
    justify-content: flex-end;
  }
  
  .button-group {
    display: flex;
    justify-content: space-between;
    width: 100%;
  }
  
  .item{
    position: relative;
    
    .item__input{
      width: 100%;
      .el-input__inner {
        padding: 0 5px;
        height: 28px;
        line-height: 28px;
      }
    }
    
    .item__txt{
      box-sizing: border-box;
      border: 1px solid transparent;
      line-height: 24px;
      width: 100%;
      padding: 0 5px;
      min-height: 28px;
    }
  }

  /* 移除数字输入框的上下箭头 */
  .number-input input::-webkit-outer-spin-button,
  .number-input input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  /* Firefox */
  .number-input input[type=number] {
    -moz-appearance: textfield;
  }

  /* 确保所有数字输入框样式统一 */
  .number-input input {
    text-align: right;
  }


  /* 取消[type='number']的input的上下箭头 */
input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}
 
input::-webkit-outer-spin-button {
  -webkit-appearance: none !important;
}
 
input[type='number'] {
  -moz-appearance: textfield;
}

  /* 未确认标签样式 */
  .unconfirmed-tag {
    display: inline-block;
    margin-left: 5px;
    color: #909399;
    font-size: 12px;
    padding: 0 5px;
    line-height: 16px;
    background-color: #F4F4F5;
    border-radius: 2px;
  }
  
  /* 状态行样式 - 用于左右排列金额和状态标签 */
  .status-row {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    width: 100%;
  }
  
  .status-row .amount {
    flex-shrink: 0;
  }
</style>
  
  

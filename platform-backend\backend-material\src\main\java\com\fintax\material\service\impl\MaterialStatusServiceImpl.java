package com.fintax.material.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.util.IoUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fintax.api.act.bean.ActBasicArchives;
import com.fintax.api.act.bean.EmpdocBean;
import com.fintax.api.act.bean.InsuranceItemBean;
import com.fintax.api.act.entity.ManageEntity;
import com.fintax.api.act.feign.AccountStatusFeignService;
import com.fintax.api.act.feign.CapitalFlowFeignService;
import com.fintax.api.act.feign.FileFeignService;
import com.fintax.api.act.query.IndexQuery;
import com.fintax.api.ans.bean.AnalysisStatusBean;
import com.fintax.api.ans.feign.AnalysisFeignService;
import com.fintax.api.material.dto.AccountinfoEntity;
import com.fintax.api.material.dto.CostInvoice;
import com.fintax.api.material.dto.ReceivedFileDto;
import com.fintax.api.material.entity.MaterialStatusEntity;
import com.fintax.api.socket.bean.Message;
import com.fintax.api.socket.feign.SocketFeignClient;
import com.fintax.common.constant.BusinessType;
import com.fintax.common.constant.FileConstant;
import com.fintax.common.constant.RedisConstant;
import com.fintax.common.service.impl.EnhancedServiceImpl;
import com.fintax.common.utils.*;
import com.fintax.material.dao.TPurchaseInvoiceBackendDao;
import com.fintax.material.dao.TSaleInvoiceBackendDao;
import com.fintax.material.entity.*;
import com.fintax.material.entity.dto.ExcelDTOInsurance;
import com.fintax.material.entity.vo.MaterialStatusVo;
import com.fintax.material.service.*;
import com.fintax.material.utils.excel.ParseExcelUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.locks.ReentrantLock;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import com.fintax.material.dao.MaterialStatusDao;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

@SuppressWarnings("all")
@RequiredArgsConstructor
@Service("materialStatusService")
@Transactional
public class MaterialStatusServiceImpl extends EnhancedServiceImpl<MaterialStatusDao, MaterialStatusEntity> implements MaterialStatusService {

    private final IReceivedFileService receivedFileService;
    private final IAnalysisedFileService analysisedFileService;
    private final IReadExcelDataService readExcelDataService;
    private final IPurchaseInvoiceService purchaseInvoiceService;
    private final PurchaseInvoiceDetailService purchaseInvoiceDetailService;
    private final ISaleInvoiceService saleInvoiceService;
    private final SaleInvoiceDetailService saleInvoiceDetailService;
    private final NodeclareInvoiceService nodeclareInvoiceService;
    private final IInsuranceService insuranceService;
    private final AccountinfoService accountinfoService;
    private final RedisTemplate<String,String> redisTemplate;
    private final SocketFeignClient socketFeignClient;
    public final String SEPARATOR = File.separator;
    private final CapitalFlowFeignService capitalFlowFeignService;


    private final FileFeignService fileFeignService;
    private final AnalysisFeignService analysisFeignService;
    private final AccountStatusFeignService accountStatusFeignService;

    private final MaterialUploadTodoService materialUploadTodoService;

    private final MaterialStatusDao materialStatusDao;

    private final TPurchaseInvoiceBackendDao tPurchaseInvoiceBackendDao;
    private final TSaleInvoiceBackendDao tSaleInvoiceBackendDao;


    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        String completeStatus = (String) params.get("completeStatus");
        String accountPeriod = (String) params.get("accountPeriod");

        String key = "";
        if (params.get("key") != null && !params.get("key").toString().isEmpty()) {
            key = params.get("key").toString();
        }
        List<ManageEntity> manageEntities = capitalFlowFeignService.searchListAct(key);
        List<String> accountCodes = manageEntities.stream().map(ManageEntity::getCode).collect(Collectors.toList());
        LambdaQueryWrapper<MaterialStatusEntity> materialStatusEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
        materialStatusEntityLambdaQueryWrapper.eq(MaterialStatusEntity::getAccountPeriod,accountPeriod);
        if (accountCodes != null && accountCodes.size() > 0){
            materialStatusEntityLambdaQueryWrapper.in(MaterialStatusEntity::getAccountCode,accountCodes);
        }else{
            materialStatusEntityLambdaQueryWrapper.in(MaterialStatusEntity::getAccountCode,Arrays.asList("FINTAX_KJHJ39438"));
        }


        if(!StrUtil.isEmpty(completeStatus)){
            materialStatusEntityLambdaQueryWrapper.eq(MaterialStatusEntity::getCompleteStatus,completeStatus);
        }

        IPage<MaterialStatusEntity> page = this.page(
                new Query<MaterialStatusEntity>().getPage(params),
                materialStatusEntityLambdaQueryWrapper
        );

        List<MaterialStatusEntity> records = page.getRecords();
        for(MaterialStatusEntity materialStatusEntity : records){
            String accountCode = materialStatusEntity.getAccountCode();
            //获取账套编号相同的一条数据
            ManageEntity manageEntity = manageEntities.stream().filter(item -> item.getCode().equals(accountCode)).findFirst().orElse(null);
            if(manageEntity !=null){
                materialStatusEntity.setAccountName(manageEntity.getSubName());
                materialStatusEntity.setAccountNature(manageEntity.getTaxtypeStr());
                materialStatusEntity.setAccountIndustry(manageEntity.getIndustryStr());
                materialStatusEntity.setCrno(manageEntity.getCrno());
            }
        }
        if (records != null && records.size() > 0){
            records.sort(Comparator.comparing((MaterialStatusEntity o) -> o.getCrno()== null ? "0" : o.getCrno()));
        }

        return new PageUtils(page);
    }

    @Override
    public Map<String, Object> queryCount(String accountPeriod) {
        Map<String, Object> map = new HashMap<>();
        //查询总条数，已完成的条数  和未完成的条数
        LambdaQueryWrapper<MaterialStatusEntity> materialStatusEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
        materialStatusEntityLambdaQueryWrapper.eq(MaterialStatusEntity::getAccountPeriod,accountPeriod);

        String key ="";
        List<ManageEntity> manageEntities = capitalFlowFeignService.searchListAct(key);
        List<String> accountCodes = manageEntities.stream().map(ManageEntity::getCode).collect(Collectors.toList());
        materialStatusEntityLambdaQueryWrapper.eq(MaterialStatusEntity::getAccountPeriod,accountPeriod);
        if (accountCodes != null && accountCodes.size() > 0){
            materialStatusEntityLambdaQueryWrapper.in(MaterialStatusEntity::getAccountCode,accountCodes);
        }else{
            materialStatusEntityLambdaQueryWrapper.in(MaterialStatusEntity::getAccountCode,Arrays.asList("FINTAX_KJHJ39438"));
        }


        Long effectiveCount = baseMapper.selectCount(materialStatusEntityLambdaQueryWrapper);
        Long finishedCount = baseMapper.selectCount(materialStatusEntityLambdaQueryWrapper.eq(MaterialStatusEntity::getCompleteStatus, "1"));
        map.put("effectiveCount",effectiveCount);
        map.put("finishedCount",finishedCount);
        return map;
    }

    @Override
    public void markNoInvoice(MaterialStatusEntity materialStatus, String type) {
        LambdaQueryWrapper<MaterialStatusEntity> wrapper = new LambdaQueryWrapper<MaterialStatusEntity>().
                eq(MaterialStatusEntity::getAccountCode, materialStatus.getAccountCode())
                .eq(MaterialStatusEntity::getAccountPeriod, materialStatus.getAccountPeriod());
        MaterialStatusEntity one = baseMapper.selectOne(wrapper);
        if(type.equals("saleInvoice")){
            Integer noSaleData = materialStatus.getNoSaleData();
            one.setNoSaleData(noSaleData);
            if(noSaleData == 1){
                one.setSaleMaterial("0份");
            }else{
                one.setSaleMaterial("");
            }

        }else if(type.equals("purchaseInvoice")){
            one.setNoPurchaseData(materialStatus.getNoPurchaseData());
            Integer noPurchaseData = materialStatus.getNoPurchaseData();
            one.setNoPurchaseData(noPurchaseData);
            if(noPurchaseData == 1){
                one.setPurchaseMaterial("0份");
            }else{
                one.setPurchaseMaterial("");
            }
        }
        updateById(one);
    }

    @Override
    public R reuploadFile(Integer id, String accountCode, String businessType) {
        // 1. 根据ID获取待处理记录
        MaterialUploadTodoEntity todoEntity = materialUploadTodoService.getById(id);
        if (todoEntity == null) {
            //throw new RuntimeException("待处理记录不存在，ID: " + id);
            return R.error("待处理记录不存在，ID: " + id);
        }

        // 2. 检查文件是否存在
        String filePath = todoEntity.getFileId(); // fileId实际存储的是文件路径
        File file = new File(filePath);
        if (!file.exists()) {
            //throw new RuntimeException("原始文件不存在，路径: " + filePath);
            return R.error("原始文件不存在，路径: " + filePath);
        }


        try {
            // 3. 获取所有账套信息
            List<ManageEntity> allAccountList = capitalFlowFeignService.listActAll();

            // 5. 创建ReceivedFileDto
            ReceivedFileDto receivedFileDto = new ReceivedFileDto();
            receivedFileDto.setFileName(file.getName());
            receivedFileDto.setFilePath(file.getAbsolutePath());
            receivedFileDto.setFileSize(file.length());
            receivedFileDto.setFileSource("reupload");
            receivedFileDto.setReceiveDate(dateTimeFmt.format(new Date()));

            // 6. 重新解析文件
            AnalysisedFile analysisedFile = reprocessFile(file, allAccountList, accountCode, businessType);

            if (analysisedFile != null && !"error".equals(analysisedFile.getState())) {
                // 7. 处理成功，上传到minio
                try {
                    MultipartFile multipartFile = FileUtils.File2MultiFile(file);
                    R uploadResult = fileFeignService.fileUpload(multipartFile, analysisedFile.getAccountName());
                    analysisedFile.setFilePath(uploadResult.get("msg").toString());

                    // 8. 保存解析结果
                    analysisedFileService.save(analysisedFile);

                    // 9. 更新资料状态
                    String currentMonth = capitalFlowFeignService.getCurrentMonth();
                    this.updateMaterialStatusByAccountCodeAndAccountPeriod(
                            analysisedFile.getAccountCode(), currentMonth, analysisedFile.getMenuCode());

                    // 10. 标记待处理记录为已处理
                    todoEntity.setClosestatus(1);
                    todoEntity.setUpdateTime(new Date());
                    todoEntity.setAccountCode(accountCode);
                    todoEntity.setBusinessType(businessType);
                    materialUploadTodoService.updateById(todoEntity);
                    // 11. 清理临时文件
                    if (file.exists()) {
                        file.delete();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    return R.error( "文件上传失败: " + e.getMessage());
                    //throw new RuntimeException("文件上传失败: " + e.getMessage(), e);
                }
            } else {
                String errorMsg = analysisedFile != null ? analysisedFile.getMessage() : "文件解析失败";
                return R.error( "文件上传失败: " + errorMsg);
                //throw new RuntimeException("重新处理文件失败: " + errorMsg);
            }

        } catch (Exception e) {
            e.printStackTrace();
            return R.error( "文件上传失败: " + e.getMessage());
            //throw new RuntimeException("重新上传处理失败: " + e.getMessage(), e);
        }
        return R.ok("文件上传成功");
    }

    @Override
    public List<CostInvoice> getCostDataByNumbers(String accountCode, List<String> invoiceNumbers) {
        return materialStatusDao.findCostDataByNumbers(accountCode, invoiceNumbers);
    }

    @Override
    public void updateImagePath(String accountCode, List<CostInvoice> costList) {
        materialStatusDao.updateCostImagePath(accountCode, costList);
    }

    /**
     * 重新处理文件
     * @param file 文件
     * @param allAccountList 所有账套列表
     * @param accountCode 指定的账套编码
     * @param businessType 指定的业务类型
     * @return 解析结果
     */
    private AnalysisedFile reprocessFile(File file, List<ManageEntity> allAccountList, String accountCode, String businessType) {
        String filePath = file.getAbsolutePath();
        String ext = FileUtil.extName(filePath).toLowerCase();

        AnalysisedFile analysisedFile = new AnalysisedFile();
        analysisedFile.setFilePath(filePath);

        try {
            if (ext.equalsIgnoreCase("pdf") || ext.equalsIgnoreCase("ofd") || ext.equalsIgnoreCase("xml")) {
                // 处理PDF/OFD/XML文件
                List<AnalysisedFile> analysisedFiles = analysisedFileService.analysisPDFXML(file.getAbsolutePath(), allAccountList);
                List<AnalysisedFile> analysisedFileList = new ArrayList<AnalysisedFile>();
                analysisedFileList.addAll(analysisedFiles);
                analysisedFileService.updateXMLPDFPath(analysisedFileList);

                //修改成本发票 图片地址 2025-07-28
                if (analysisedFileList != null && analysisedFileList.size() > 0){
                    updateCostPdf(analysisedFileList,accountCode);
                }

            } else if (ext.equalsIgnoreCase("xls") || ext.equalsIgnoreCase("xlsx")) {
                // 处理Excel文件 - 对于重新上传，我们需要简化处理
                // 直接设置基本信息，不进行复杂的Excel解析
                analysisedFile.setOriginName(file.getName());
                analysisedFile.setOriginPath(filePath);
                analysisedFile.setNewName(file.getName());
                analysisedFile.setFileSize(file.length());
                analysisedFile.setFileType(ext);
                analysisedFile.setReceiveDate(dateTimeFmt.format(new Date()));
                analysisedFile.setUploadType("ExcelInvoice");

            } else {
                analysisedFile.setState("error");
                analysisedFile.setMessage("不支持的文件类型: " + ext);
                return analysisedFile;
            }

            // 如果指定了账套编码，设置账套信息
            if (accountCode != null && !accountCode.trim().isEmpty()) {
                // 查找指定的账套信息
                ManageEntity targetAccount = allAccountList.stream()
                        .filter(account -> accountCode.equals(account.getCode()))
                        .findFirst()
                        .orElse(null);

                if (targetAccount != null) {
                    analysisedFile.setAccountCode(accountCode);
                    analysisedFile.setAccountName(targetAccount.getName());
                    //analysisedFile.setAccountId(targetAccount.getId());

                    // 设置业务类型对应的菜单编码
                    if (businessType != null && !businessType.trim().isEmpty()) {
                        analysisedFile.setMenuCode(businessType);
                    }

                    // 清除错误状态
                    analysisedFile.setState(null);
                    analysisedFile.setMessage(null);


                    //处理excel需要用的基础数据
                    String taxSystem = redisTemplate.opsForValue().get(RedisConstant.REDIS_KEY_TAXSYSTEM);
                    JSONObject taxJot = JSONObject.parseObject(taxSystem);
                    JSONObject rulesObj = (JSONObject) taxJot.get("invoice");
                    String colruleString = redisTemplate.opsForValue().get(RedisConstant.REDIS_KEY_COLRULES);
                    com.alibaba.fastjson.JSONArray colRules = com.alibaba.fastjson.JSONArray.parseArray(colruleString);

                    String detailClassify = redisTemplate.opsForValue().get(RedisConstant.REDIS_KEY_DETAILCLASSIFY);
                    com.alibaba.fastjson.JSONArray classifyArray = com.alibaba.fastjson.JSONArray.parseArray(detailClassify);

                    if (file.exists()) {
                        byte[] stream = new byte[0];
                        InputStream inputStream = null;
                        try {
                            inputStream = Files.newInputStream(file.toPath());
                            stream = IoUtils.toByteArray(inputStream);
                        } catch (IOException e) {
                            throw new RuntimeException(e);
                        } finally {
                            try {
                                inputStream.close();
                            } catch (IOException e) {
                                throw new RuntimeException(e);
                            }
                        }
                        Map<Integer, Map<String, Object>> headMap = ParseExcelUtil.getHeadMap(stream);
                        Map<Integer, Map<String, Object>> dataMap = ParseExcelUtil.getDataMap(headMap, stream);

                        //读取excel文件内容
                        Map<String, Object> stringObjectMap = readExcelDataService.parseInvoiceExcel(headMap,dataMap,rulesObj,colRules,classifyArray);
                        System.out.println(stringObjectMap);
                        analysisedFile = handleInvoiceExcelData(stringObjectMap,analysisedFile);
                    }
                } else {
                    analysisedFile.setState("error");
                    analysisedFile.setMessage("指定的账套编码不存在: " + accountCode);
                }
            } else {
                analysisedFile.setState("error");
                analysisedFile.setMessage("必须指定账套编码");
            }

        } catch (Exception e) {
            analysisedFile.setState("error");
            analysisedFile.setMessage("文件处理异常: " + e.getMessage());
        }

        return analysisedFile;
    }

    private void updateCostPdf(List<AnalysisedFile> analysisedFileList,String accountCode) {
        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    List<AnalysisedFile> costPdfList = analysisedFileList.stream().filter(analysisedFile -> !StringUtil.isEmpty(analysisedFile.getInvoiceNumber())).collect(Collectors.toList());
                    List<String> invoiceNumbers = costPdfList
                            .stream().map(AnalysisedFile::getInvoiceNumber).collect(Collectors.toList());
                    if (invoiceNumbers != null && invoiceNumbers.size() > 0){
                        List<CostInvoice> costList = getCostDataByNumbers(accountCode, invoiceNumbers);
                        if (costList != null && costList.size() > 0){
                            costList.stream().forEach(costInvoice -> {
                                AnalysisedFile pdfFile = analysisedFileList.stream().filter(x -> StringUtil.equals(costInvoice.getInvoiceNumber(),x.getInvoiceNumber())).findFirst().orElse(null);
                                if (pdfFile != null && !StringUtil.isEmpty(pdfFile.getFilePath())){
                                    costInvoice.setImagePath(pdfFile.getFilePath());
                                }
                            });

                            updateImagePath(accountCode,costList);
                        }
                    }
                }catch (Exception e){
                    e.printStackTrace();
                }
            }
        }).start();
    }


    @Override
    public MaterialStatusVo getListInfo(IndexQuery query) {
        MaterialStatusVo materialStatusVo = new MaterialStatusVo();
        return materialStatusVo;
    }

    /**
     * 将某个账套的数据全部设置为1
     * @param materialStatus
     */
    @Override
    public void completeStatus(MaterialStatusEntity materialStatus) {
        materialStatus.setCompleteStatus("1");
        if(StrUtil.isEmpty(materialStatus.getSaleMaterial())){
            materialStatus.setSaleMaterial("0份");
            materialStatus.setNoSaleData(1);
        }
        if(StrUtil.isEmpty(materialStatus.getPurchaseMaterial())){
            materialStatus.setPurchaseMaterial("0份");
            materialStatus.setNoPurchaseData(1);
        }
        if(StrUtil.isEmpty(materialStatus.getNodeclareMaterial())){
            materialStatus.setNodeclareMaterial("0份");
        }
        updateById(materialStatus);

        //调用老王分析接口
        AnalysisStatusBean analysisStatusBean = new AnalysisStatusBean();
        analysisStatusBean.setAccountid(materialStatus.getAccountId());
        analysisStatusBean.setDbCode(materialStatus.getAccountCode());
        analysisStatusBean.setIsUpload(1);
        analysisFeignService.updateStatus(analysisStatusBean);
    }

    /**
     * 结账后初始化下一个期间的资料状态
     */
    @Override
    public void initMaterialStatus(String accountPeriod) {
        List<ManageEntity> manageEntities = capitalFlowFeignService.listActAll();
        List<MaterialStatusEntity> list = new ArrayList<>();
        for(ManageEntity manageEntity : manageEntities){
            MaterialStatusEntity materialStatusEntity = new MaterialStatusEntity();
            materialStatusEntity.setAccountId(manageEntity.getId());
            materialStatusEntity.setAccountCode(manageEntity.getCode());
            materialStatusEntity.setAccountPeriod(accountPeriod);
            materialStatusEntity.setChannelId(manageEntity.getChannelId());
            list.add(materialStatusEntity);
        }
        saveOrUpdateBatchByColumn(list,
                item-> new LambdaQueryWrapper<>(MaterialStatusEntity.class)
                        .eq(MaterialStatusEntity::getAccountId,item.getAccountId())
                        .eq(MaterialStatusEntity::getAccountPeriod,accountPeriod));

        //将上月已经上传了发票PDF文件的发票从t_sale_invoice_backend 和 t_purchase_invoice_backend表中删除
        String prevMonth = DateUtils.getPrevMonth(accountPeriod);
        tPurchaseInvoiceBackendDao.delete(new LambdaQueryWrapper<TPurchaseInvoiceBackendEntity>()
                .eq(TPurchaseInvoiceBackendEntity::getIshavefile,1)
        );

        tSaleInvoiceBackendDao.delete(new LambdaQueryWrapper<TSaleInvoiceBackendEntity>()
                .eq(TSaleInvoiceBackendEntity::getIshavefile,1)
        );

    }

    /**
     * 跟新当前期间的数据状态列表。
     * @param accountPeriod
     */
    @Override
    public Boolean updateMaterialStatusByAccountPeriod(String accountPeriod) {
        if(StringUtil.isEmpty(accountPeriod)){
            return false;
        }
        //首先查询出各个账套的账套状态集合。

        List<MaterialStatusEntity> list = baseMapper.queryListStatusByAccountPeriod(accountPeriod);
        //过滤未完结的数据
//        list.stream().filter(item->!item.getCompleteStatus().equals("1")).forEach(item->{
//            if(!StrUtil.isEmpty(item.getSaleMaterial()) && !StrUtil.isEmpty(item.getPurchaseMaterial())
//                && !StrUtil.isEmpty(item.getNodeclareMaterial())){
//                item.setCompleteStatus("1");
//                //调用老王分析接口
//                AnalysisStatusBean analysisStatusBean = new AnalysisStatusBean();
//                analysisStatusBean.setDbCode(item.getAccountCode());
//                analysisStatusBean.setIsUpload(1);
//                analysisFeignService.updateStatus(analysisStatusBean);
//            }else{
//                item.setCompleteStatus("0");
//            }
//        });
        boolean b = updateBatchById(list);
        return b;
    }

    /*
    根据给定的账套编号和期间，更新当前期间状态。
     */
    @Override
    public Boolean updateMaterialStatusByAccountCodeAndAccountPeriod(String accountCode,String accountPeriod) {
        if(StringUtil.isEmpty(accountCode) || StringUtil.isEmpty(accountPeriod)){
            return false;
        }
        //首先查询出各个账套的账套状态集合。
        MaterialStatusEntity materialStatusEntity = baseMapper.selectListStatusByAccountCodeAndAccountPeriod(accountCode,accountPeriod);
        //过滤当前账套的数据 取一个
        if(materialStatusEntity !=null){
//            if(!StrUtil.isEmpty(materialStatusEntity.getSaleMaterial()) && !StrUtil.isEmpty(materialStatusEntity.getPurchaseMaterial())
//                    && !StrUtil.isEmpty(materialStatusEntity.getNodeclareMaterial())){
//                materialStatusEntity.setCompleteStatus("1");
//            }else{
//                materialStatusEntity.setCompleteStatus("0");
//            }

            boolean b = updateById(materialStatusEntity);
            return b;
        }

        String status = "0";
        //2025-02-28修改  新增了一个账套状态表，记录一些单据的状态，其中包含开票资料，所以再此将开票资料状态置  设置到此账套的状态
        if(!StrUtil.isEmpty(materialStatusEntity.getSaleMaterial()) || !StrUtil.isEmpty(materialStatusEntity.getNodeclareMaterial())){
            status = "1";
        }
        accountStatusFeignService.updateByCode(accountPeriod, accountCode, BusinessType.SALEINVOICE, status);
        return true;
    }

    @Override
    public Boolean updateMaterialStatusByAccountCodeAndAccountPeriod(String accountCode,String accountPeriod,String businessType) {
        if(StringUtil.isEmpty(accountCode) || StringUtil.isEmpty(accountPeriod)){
            return false;
        }
        //首先查询出各个账套的账套状态集合。
        MaterialStatusEntity materialStatusEntity = baseMapper.selectListStatusByAccountCodeAndAccountPeriod(accountCode,accountPeriod);
        //过滤当前账套的数据 取一个
        if(materialStatusEntity !=null){
//            if(!StrUtil.isEmpty(materialStatusEntity.getSaleMaterial()) && !StrUtil.isEmpty(materialStatusEntity.getPurchaseMaterial())
//                    && !StrUtil.isEmpty(materialStatusEntity.getNodeclareMaterial())){
//                materialStatusEntity.setCompleteStatus("1");
//            }else{
//                materialStatusEntity.setCompleteStatus("0");
//            }
            if("saleInvoiceExcel".equals(businessType)){
                materialStatusEntity.setPurchaseMaterial(null);
                materialStatusEntity.setNodeclareMaterial(null);
                if(materialStatusEntity.getSaleMaterial() == null){
                    materialStatusEntity.setSaleMaterial("");
                }else{
                    materialStatusEntity.setNoSaleData(0);
                }
            }else if("purchaseInvoiceExcel".equals(businessType)){
                materialStatusEntity.setSaleMaterial(null);
                materialStatusEntity.setNodeclareMaterial(null);
                if(materialStatusEntity.getPurchaseMaterial() == null){
                    materialStatusEntity.setPurchaseMaterial("");
                }else{
                    materialStatusEntity.setNoPurchaseData(0);
                }
            }else if("nodeclareInvoice".equals(businessType)){
                materialStatusEntity.setPurchaseMaterial(null);
                materialStatusEntity.setSaleMaterial(null);
                if(materialStatusEntity.getNodeclareMaterial() == null){
                    materialStatusEntity.setNodeclareMaterial("");
                }
            }
            boolean b = updateById(materialStatusEntity);
            return b;
        }

        String status = "0";
        //2025-02-28修改  新增了一个账套状态表，记录一些单据的状态，其中包含开票资料，所以再此将开票资料状态置  设置到此账套的状态
        if(!StrUtil.isEmpty(materialStatusEntity.getSaleMaterial()) || !StrUtil.isEmpty(materialStatusEntity.getNodeclareMaterial())){
            status = "1";
        }
        accountStatusFeignService.updateByCode(accountPeriod, accountCode, BusinessType.SALEINVOICE, status);
        return true;
    }

    /**
     * 前台取消勾选时候，要先删除单据的数据，然后再将状态更新为null
     * @param materialStatus
     */
    @Override
    public R updateAndDeleteData(MaterialStatusEntity materialStatus) {
        String type = materialStatus.getUpdateColName();
        Map<String,Object> conditions = new HashMap<>();

        switch (type){
            case "purchaseMaterial":
                //查询是否存在被使用的数据，如果存在，提示错误信息。
                boolean b = purchaseInvoiceService.checkExistIsUsedData(materialStatus.getAccountPeriod(), materialStatus.getAccountCode());
                if(b){
                    return R.error(2,"该数据已被使用，无法删除！");
                }
                conditions.clear();
                conditions.put("account_code",materialStatus.getAccountCode());
                conditions.put("upload_period",materialStatus.getAccountPeriod());
                purchaseInvoiceDetailService.removeByMap(conditions);
                purchaseInvoiceService.removeByMap(conditions);
                materialStatus.setPurchaseMaterial("");
                break;
            case "saleMaterial":
                conditions.clear();
                conditions.put("account_code",materialStatus.getAccountCode());
                conditions.put("account_period",materialStatus.getAccountPeriod());
                //conditions.put("is_add",0);
                saleInvoiceDetailService.removeByMap(conditions);
                saleInvoiceService.removeByMap(conditions);
                materialStatus.setPurchaseMaterial("");
                break;

//            case "insuranceMaterial":
//                insuranceService.deleteInsuranceByMonthAndAccCode(materialStatus.getAccountCode(),materialStatus.getAccountPeriod());
//                materialStatus.setInsuranceMaterial("");
//                break;

            case "nodeclareMaterial":
                conditions.clear();
                conditions.put("account_code",materialStatus.getAccountCode());
                conditions.put("account_period",materialStatus.getAccountPeriod());
                //conditions.put("is_add",1);
                nodeclareInvoiceService.removeByMap(conditions);
                materialStatus.setNodeclareMaterial("");
                break;

        }
        updateById(materialStatus);
        return R.ok();
    }

    /*
    * 手动上传文件方法
    *   File file1 = FileUtils.multiFile2File(file);
                    ReceivedFileDto receivedAttach = new ReceivedFileDto();
                    receivedAttach.setFileName(file1.getName());
                    receivedAttach.setFilePath(file1.getAbsolutePath());
                    receivedAttach.setFileSize(file1.length());
                    receivedAttach.setFileSource("browser");
                    receivedAttach.setReceiveDate(dateTimeFmt.format(new Date()));

                    list.add(receivedAttach);
     */
    public SimpleDateFormat dateTimeFmt = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    @Override
    public void handleFileUpload(File[] files, String from) {
        List<ReceivedFileDto> receivedList = new ArrayList<>();

        if (files != null && files.length > 0) {
            for (File file : files) {
                if (file == null) {
                    System.err.println("警告: 文件对象为null，跳过处理");
                    continue;
                }

                if (!file.exists()) {
                    System.err.println("警告: 文件不存在，路径: " + file.getAbsolutePath() + "，跳过处理");
                    continue;
                }

                try {
                    // 创建ReceivedFileDto对象
                    ReceivedFileDto receivedAttach = new ReceivedFileDto();
                    receivedAttach.setFileName(file.getName());
                    receivedAttach.setFilePath(file.getAbsolutePath());
                    receivedAttach.setFileSize(file.length());
                    receivedAttach.setFileSource("browser");
                    receivedAttach.setReceiveDate(dateTimeFmt.format(new Date()));
                    receivedList.add(receivedAttach);
                } catch (Exception e) {
                    System.err.println("处理文件失败: " + file.getName() + ", 错误: " + e.getMessage());
                    e.printStackTrace();
                }
            }
        }

        if (!receivedList.isEmpty()) {
            analysisFiles(receivedList, from);
        } else {
            System.err.println("警告: 没有有效的文件可供处理");
        }
    }
    public void clearOldFile(String filePath){
        File directory = new File(filePath);
        if (directory.exists() && directory.isDirectory()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isFile()) { // 确保是文件而非目录
                        file.delete();
                    }
                }
            }
        }
    }
    @Override
    public List<ActBasicArchives> getDocList(String accCode, String type) {
        return capitalFlowFeignService.ansyBasicArchivesByAccCode(accCode,type);
    }



    /*
        文件收取方法
     */
    @Override
    public void analysisFiles(List<ReceivedFileDto> list,String from) {

        List<AnalysisedFile> analysisedFileList = new ArrayList<>();

        String currentMonth = capitalFlowFeignService.getCurrentMonth();

        if(!list.isEmpty()){
            boolean isExcel = false;
            //基础数据准备
            List<ManageEntity> allAccountList = capitalFlowFeignService.listActAll();

            //使用hutool包的Beanutil将List<ReceivedFileDto> 转为List<ReceivedFile>
            List<ReceivedFile> receivedFileList = BeanUtil.copyToList(list, ReceivedFile.class);
            //分析文件。
            StringBuffer messgeString = new StringBuffer();
            boolean haveZipFile = false;
            List<ReceivedFile> zipFileList = new ArrayList<>();
            for(ReceivedFile receivedFile : receivedFileList){
                AnalysisedFile analysisedFile = new AnalysisedFile();
                String filePath = receivedFile.getFilePath();
                analysisedFile.setFilePath(filePath);
                File file = new File(filePath);
                String ext = FileUtil.extName(filePath).toLowerCase();
                if(ext.equalsIgnoreCase("xls") ||ext.equalsIgnoreCase("xlsx")) {//excel
                    isExcel = true;
                    //处理excel需要用的基础数据
                    String taxSystem = redisTemplate.opsForValue().get(RedisConstant.REDIS_KEY_TAXSYSTEM);
                    JSONObject taxJot = JSONObject.parseObject(taxSystem);
                    JSONObject rulesObj = (JSONObject) taxJot.get("invoice");
                    String colruleString = redisTemplate.opsForValue().get(RedisConstant.REDIS_KEY_COLRULES);
                    com.alibaba.fastjson.JSONArray colRules = com.alibaba.fastjson.JSONArray.parseArray(colruleString);

                    String detailClassify = redisTemplate.opsForValue().get(RedisConstant.REDIS_KEY_DETAILCLASSIFY);
                    com.alibaba.fastjson.JSONArray classifyArray = com.alibaba.fastjson.JSONArray.parseArray(detailClassify);

                    if(file.exists()){
                        byte[] stream = new byte[0];
                        InputStream inputStream =null;
                        try {
                            inputStream = Files.newInputStream(file.toPath());
                            stream = IoUtils.toByteArray(inputStream);
                        } catch (IOException e) {
                            throw new RuntimeException(e);
                        }finally {
                            try {
                                inputStream.close();
                            } catch (IOException e) {
                                throw new RuntimeException(e);
                            }
                        }
                        Map<Integer, Map<String, Object>> headMap = ParseExcelUtil.getHeadMap(stream);
                        Map<Integer, Map<String, Object>> dataMap = ParseExcelUtil.getDataMap(headMap, stream);

                        //分析文件
                        analysisedFile = analysisedFileService.parseExcelAndAssembleFileInfo(file,receivedFile,allAccountList,headMap,dataMap);
                        if("error".equals(analysisedFile.getState())){
                            receivedFile.setState(analysisedFile.getState());
                            receivedFile.setMessage(analysisedFile.getMessage());
                            messgeString.append("文件："+file.getName()+"，上传错误，错误原因：").append(analysisedFile.getMessage());
                            analysisedFileList.add(analysisedFile);
                            continue;
                        }
                        if(analysisedFile.getAccountCode() == null){
                            analysisedFile.setState("error");
                            analysisedFile.setMessage("没有找到文件所属账套！");
                            analysisedFileList.add(analysisedFile);
                            messgeString.append("文件："+file.getName()+"，上传错误，错误原因：").append("没有找到文件所属账套！").append("\n");
                            continue;
                        }
                        if(analysisedFile.getMenuCode() == null){
                            analysisedFile.setState("error");
                            analysisedFile.setMessage("没有找到文件所属单据！");
                            analysisedFileList.add(analysisedFile);
                            messgeString.append("文件："+file.getName()+"，上传错误，错误原因：").append("没有找到文件所属单据！").append("\n");
                            continue;
                        }

                        if(analysisedFile.getState() ==null || !"error".equals(analysisedFile.getState())){

                            //读取excel文件内容
                            Map<String, Object> stringObjectMap = readExcelDataService.parseInvoiceExcel(headMap,dataMap,rulesObj,colRules,classifyArray);
                            System.out.println(stringObjectMap);
                            analysisedFile = handleInvoiceExcelData(stringObjectMap,analysisedFile);
                            if("error".equals(analysisedFile.getState()) ){
                                messgeString.append("文件："+file.getName()+"，上传错误，错误原因：").append(analysisedFile.getMessage());
                                analysisedFileList.add(analysisedFile);
                                continue;
                            }
                            updateMaterialStatusByAccountCodeAndAccountPeriod(analysisedFile.getAccountCode(),currentMonth,analysisedFile.getMenuCode());
                            analysisedFileList.add(analysisedFile);

                        }

                    }

                }
                else if(FileUtils.isFileCompressed(filePath)){
                    haveZipFile = true;
                    zipFileList.add(receivedFile);
                    List<AnalysisedFile> compressFileList = analysisedFileService.parseCompressFileInfo(filePath, allAccountList);
                    analysisedFileList.addAll(compressFileList);

                    //更新文件地址到数据的xmlPath字段。

                }else if(ext.equalsIgnoreCase("pdf") ||ext.equalsIgnoreCase("PDF")
                        || ext.equalsIgnoreCase("ofd") ||ext.equalsIgnoreCase("OFD")
                        || ext.equalsIgnoreCase("xml") ||ext.equalsIgnoreCase("XML")){
                    List<AnalysisedFile> analysisedFile1 = analysisedFileService.analysisPDFXML(file.getAbsolutePath(), allAccountList);
                    if(!analysisedFile1.isEmpty()){
                        analysisedFileList.addAll(analysisedFile1);
                    }

                }
                else{
                    receivedFile.setState("error");
                    receivedFile.setMessage("非需要的文件类型！");
                    messgeString.append("文件："+file.getName()+"，上传错误，错误原因：").append("非需要的文件类型！").append("\n");

                }
            }

            if(from.equals("front") && messgeString.length()>0){
                Message message = new Message("material","error;"+messgeString.toString(),SecurityUtils.getCurrentUsername());
                socketFeignClient.sendMessage(message);
            }else if(from.equals("front") && messgeString.length() ==0){
                Message message = new Message("material","success;文件上传成功！",SecurityUtils.getCurrentUsername());
                socketFeignClient.sendMessage(message);
            }
            if(list.size()>0){
                receivedFileService.saveOrUpdateBatch(receivedFileList);
            }
            if(analysisedFileList.size()>0){
                analysisedFileList.forEach(analysisedFile -> {
                    if(!"error".equals(analysisedFile.getState())){
                        File file1 = new File(analysisedFile.getOriginPath());
                        if (!file1.exists()) {
                            System.err.println("警告: 文件不存在，无法上传到Minio: " + analysisedFile.getOriginPath());
                            return; // 跳过这个文件的处理
                        }
                        if(StrUtil.isEmpty(analysisedFile.getAccountName()) ){
                            System.err.println("警告: 无法找到发票所属账套，无法上传到Minio: " + analysisedFile.getOriginPath());
                            return; // 跳过这个文件的处理
                        }
                        try {
                            // 确保文件可读
                            if (!file1.canRead()) {
                                System.err.println("警告: 文件不可读取: " + analysisedFile.getOriginPath());
                                return;
                            }
                            // 创建MultipartFile对象
                            MultipartFile multipartFile = FileUtils.File2MultiFile(file1);
                            // 上传到minio
                            R r = fileFeignService.fileUpload(multipartFile, analysisedFile.getAccountName());

                            // 上传成功后直接删除原文件，不再移动到handled文件夹
                            if (r.getCode() == 0) {
                                analysisedFile.setFilePath(r.get("msg").toString());
                            }
                        } catch (Exception e) {
                            System.err.println("处理文件时出错: " + e.getMessage() + ", 文件路径: " + analysisedFile.getOriginPath());
                            e.printStackTrace();
                        }
                    }else{//出现了错误，则保存错误信息到数据库  展示给前端用户进行后续操作
                        //2025-07-10如果购买方和销售方都存在账套列表内，保存到待处理文件表格，返回前台，让用户选择。
                        MaterialUploadTodoEntity todo = new MaterialUploadTodoEntity();
                        // todo.setFileId(analysisedFile.get());
                        todo.setAccountPeriod(currentMonth);
                        todo.setFileId(analysisedFile.getOriginPath());
                        todo.setFileName(analysisedFile.getOriginName());
                        todo.setSellerCode(analysisedFile.getSellerCode());
                        todo.setSellerName(analysisedFile.getSellerName());
                        todo.setBuyerCode(analysisedFile.getBuyerCode());
                        todo.setBuyerName(analysisedFile.getBuyerName());
                        todo.setErrorMsg(analysisedFile.getMessage());
                        todo.setClosestatus(0);
                        todo.setErrorType(1);
                        todo.setChannelId(SecurityUtils.getCurrentUser().getChannelId());
                        todo.setCreateTime(new Date());
                        materialUploadTodoService.save(todo);

                    }

                });

                //将解析后的数据保存在analysisFile中
                analysisedFileService.saveBatch(analysisedFileList);
                //更新t_purchase_invoice_excel 或 t_sale_invoice_excel 表中的地址
                analysisedFileService.updateXMLPDFPath(analysisedFileList);
                analysisedFileService.updateBackendXMLPDFPath(analysisedFileList);

                analysisedFileList.forEach(analysisedFile -> {

                    File file1 = new File(analysisedFile.getOriginPath());

                    try {

                        // 删除文件
                        if ( file1.exists()) {
                            boolean deleted = file1.delete();
                            if (!deleted) {
                                System.err.println("警告: 无法删除源文件: " + file1.getAbsolutePath());
                            }
                        }
                    } catch (Exception e) {
                        System.err.println("处理文件时出错: " + e.getMessage() + ", 文件路径: " + analysisedFile.getOriginPath());
                        e.printStackTrace();

                    }
                });




            }
            //如果有压缩包  删除解压后的文件
            if(haveZipFile){
                for(ReceivedFile receivedFile1:zipFileList){
                    String fileName2 = FileUtil.getPrefix(receivedFile1.getFilePath());
                    File file = new File(receivedFile1.getFilePath());
                    File unzipFile = new File(receivedFile1.getFilePath());
                    if(unzipFile.exists()){
                        FileUtil.del(unzipFile);
                    }
                    //将压缩包移到handled文件夹
//                    AnalysisedFile analysisedFile1 = new AnalysisedFile();
//                    analysisedFile1.setOriginPath(receivedFile1.getFilePath());
                    //removeFileToHandled(analysisedFile1);

                }
            }
        }

    }
    /*
    * 处理组装社保数据
     */
    private AnalysisedFile handleInsuranceExcelData(Map<String, Object> dataMap, AnalysisedFile analysisedFile) {
        if("error".equals(dataMap.get("state").toString())){
            analysisedFile.setState("error");
            analysisedFile.setMessage(dataMap.get("message").toString());
            return analysisedFile;
        }

        List<EmpdocBean> empList = capitalFlowFeignService.listEmpByAccCode(analysisedFile.getAccountCode());
        List<InsuranceItemBean> itemList = capitalFlowFeignService.listInsuranceItemByAccCode(analysisedFile.getAccountCode());

        List<ExcelDTOInsurance> dataList1 = (List<ExcelDTOInsurance>) dataMap.get("data");

        if(dataList1.isEmpty()){
            analysisedFile.setState("error");
            analysisedFile.setMessage("没有读取到数据！请检查！");
            return analysisedFile;
        }

        Insurance insurance = new Insurance();
        List<Map<String,Object>> allList = new ArrayList<>();
        for (ExcelDTOInsurance dto : dataList1) {
            String empdocName = dto.getEmpdocName();
            for(EmpdocBean empdocBean : empList){
                if(empdocName.equals(empdocBean.getEmpName())){
                    Map<String,Object> empPd = new LinkedHashMap<>();
                    String wu0comppart = CalculateUtil.add(dto.getWu0comppart(), dto.getPluswu0comppart());
                    String wu0perspart = dto.getWu0personpart();
                    String wu1comppart = dto.getWu1comppart();
                    String wu1perspart = dto.getWu1personpart();
                    String wu2comppart = dto.getWu2comppart();
                    String wu4comppart = dto.getWu4comppart();
                    String wu4perspart = dto.getWu4personpart();

                    empPd.put("empdocid",empdocBean.getEmpId());
                    empPd.put("empdoc_name",empdocName);
                    empPd.put("dept_name",empdocBean.getDeptName());
                    empPd.put("50-comppart",wu0comppart);
                    empPd.put("50-personpart",wu0perspart);
                    empPd.put("51-comppart",wu1comppart);
                    empPd.put("51-personpart",wu1perspart);
                    empPd.put("52-comppart",wu2comppart);
                    empPd.put("54-comppart",wu4comppart);
                    empPd.put("54-personpart",wu4perspart);
                    String compparttotal = CalculateUtil.add(CalculateUtil.add(CalculateUtil.add(wu0comppart,wu1comppart),wu2comppart),wu4comppart);
                    String persparttotal =CalculateUtil.add( CalculateUtil.add(wu0perspart,wu1perspart),wu4perspart);
                    String totalparttotal =  CalculateUtil.add(compparttotal,persparttotal);
                    empPd.put("compparttotal",compparttotal);
                    empPd.put("personparttotal",persparttotal);
                    empPd.put("totalparttotal",totalparttotal);

                    allList.add(empPd);
                    break;
                }
            }
            //System.out.println(JSON.toJSONString(dto));
        }

        //合计行  数据构建
        List<Map<String,Object>> footerList=new ArrayList<>();
        Map<String,Object> footerPd=new HashMap<>();
        footerPd.put("empdoc_name", "合计");
        for(Map<String,Object> empPd :allList){
            for (String key : empPd.keySet()) {
                if(key.contains("-") || key.equals("basemoney") || key.equals("compparttotal")|| key.equals("personparttotal") || key.equals("totalparttotal")){
                    if(footerPd.get(key) !=null){
                        footerPd.put(key,
                                CalculateUtil.add(footerPd.get(key).toString().replace( ",", "")
                                        , String.valueOf(empPd.get(key)).replace( ",", "") ));

                    }else{
                        footerPd.put(key, String.valueOf(empPd.get(key)).replace( ",", "") );
                    }
                }

            }
        }
        footerList.add(footerPd);

        Map<String,Object> mxPd = new HashMap<>();
        mxPd.put("total",allList.size());
        mxPd.put("rows",allList);
        mxPd.put("footer",footerList);
        mxPd.put("itemNames",itemList);
        String jsonString = JSON.toJSONString(mxPd);

        analysisedFile.setState("success");
        analysisedFile.setMessage("导入成功");

        String accountPeriod = analysisedFile.getAccountperiod();
        insurance.setAccountCode(analysisedFile.getAccountCode());
        insurance.setInsurancemonth(accountPeriod);
        insurance.setCreatedate(DateUtils.getMaxDayOfMonth(accountPeriod));
        insurance.setTotalmoney(Double.parseDouble(footerPd.get("totalparttotal").toString()));
        insurance.setPersontotalmoney(Double.parseDouble(footerPd.get("personparttotal").toString()));
        insurance.setCalnorinsurancemx(jsonString);
        insurance.setCalnorinsurancemxdata(JSON.toJSONString(allList));
        insurance.setItemNames(JSON.toJSONString(itemList));
        insurance.setEmpCount(allList.size());

        List<InsuranceDetail> mxlist = new ArrayList<>();
        for (Map<String,Object> mx : allList) {
            for (InsuranceItemBean item : itemList) {
                String code = item.getInsuranceitemscode();
                InsuranceDetail insuranceDetail= new InsuranceDetail();
                insuranceDetail.setEmpdocid(Integer.parseInt(mx.get("empdocid").toString()));
                insuranceDetail.setItem(code);
                insuranceDetail.setAccountCode(analysisedFile.getAccountCode());
                insuranceDetail.setInsurancemonth(accountPeriod);
                if(mx.get(code) != null){
                    insuranceDetail.setMoney(Double.parseDouble(mx.get(code).toString()));
                }else{
                    insuranceDetail.setMoney(0d);
                }
                mxlist.add(insuranceDetail);
            }
        }
        insurance.setInsuranceDetailList(mxlist);

        int i = insuranceService.insertInsurance(insurance);
        System.out.println(i);

        return analysisedFile;
    }

    /*
     * 处理组装发票数据
     */

    private static final ReentrantLock lock = new ReentrantLock();
    public AnalysisedFile handleInvoiceExcelData(Map<String,Object> dataMap,AnalysisedFile analysisedFile){
        if("error".equals(dataMap.get("state").toString())){
            analysisedFile.setState("error");
            analysisedFile.setMessage(dataMap.get("message").toString());
            return analysisedFile;
        }
       // String uploadAccoungntPeriod = DateUtil.format(new Date(), "yyyy-MM");
        String uploadAccoungntPeriod = capitalFlowFeignService.getCurrentMonth();
        String accountCode = analysisedFile.getAccountCode();
        String business_type = analysisedFile.getMenuCode();

        //2024-04-08修改逻辑。所有数据已汇总信息为准。从汇总信息里提取主表数据和明细数据。
        List<LinkedHashMap> totalList = new ArrayList<>();
        if (dataMap.get("信息汇总表") == null){
            totalList = (List<LinkedHashMap>) dataMap.get("detail");
        }else{
            totalList = (List<LinkedHashMap>) dataMap.get("信息汇总表");
        }
        List<LinkedHashMap> baseList = new ArrayList<>();
        if(dataMap.get("发票基础信息") != null){
            baseList = (List<LinkedHashMap>) dataMap.get("发票基础信息");
        }else{
            baseList = (List<LinkedHashMap>) dataMap.get("main");
        }

        List<PurchaseInvoice> mainList = new ArrayList<>();
        List<PurchaseInvoiceDetail> detailList = new ArrayList<>();
        //遍历汇总表，组装数据
        double mx_moneyTotal = 0d;
        double mx_taxmoneyTotal = 0d;

        double base_moneyTotal = 0d;
        double base_taxmoneyTotal = 0d;



        AccountinfoEntity accountinfoByAccountCode = accountinfoService.getAccountinfoByAccountCode(accountCode);
        String taxNum = "";
        String companyName = "";
        if(accountinfoByAccountCode !=null && accountinfoByAccountCode.getTaxnum()!=null){
            taxNum = accountinfoByAccountCode.getTaxnum().trim().replace(" ","");
        }
        if(accountinfoByAccountCode !=null && accountinfoByAccountCode.getCompname()!=null){
            companyName = Convert.toDBC(accountinfoByAccountCode.getCompname().trim()) ;
        }

        Iterator<LinkedHashMap> detailIterator = totalList.iterator();
        while (detailIterator.hasNext()) {
            PurchaseInvoice mainData = new PurchaseInvoice();
            PurchaseInvoiceDetail detailData = new PurchaseInvoiceDetail();
            LinkedHashMap mainPd = detailIterator.next();
            //将作废的数据移除
            if(mainPd.get("invoiceStatus") !=null
                    && ("已作废".equals(mainPd.get("invoiceStatus".toString())) || "作废".equals(mainPd.get("invoiceStatus".toString())))){
                detailIterator.remove();
                continue;
            }


            //获取符合规则的发票号码
            String checkedInvoiceNumber = getInvoiceNumber(mainPd);
            //如果发票号码不符合规则，移除
            if("".equals(checkedInvoiceNumber)){
                detailIterator.remove();
                continue;
            }
            if((mainPd.get("sellerName") ==null
                    || "".equals(mainPd.get("sellerName").toString()))
                    && (mainPd.get("buyerName") ==null
                    || "".equals(mainPd.get("buyerName").toString()))){
                detailIterator.remove();
                continue;
            }
            if((mainPd.get("taxTypeName") ==null
                    || "".equals(mainPd.get("taxTypeName").toString()))
                    && (mainPd.get("taxTypeCode") ==null
                    || "".equals(mainPd.get("taxTypeCode").toString()))){
                detailIterator.remove();
                continue;
            }

            //这种汇总类的数据移除掉   有发票号码，没有数量  单价
            if(mainPd.get("taxTypeName")!=null && !mainPd.get("taxTypeName").toString().contains("*")
                    && (mainPd.get("taxTypeName").toString().contains("详见销货清单") || mainPd.get("taxTypeName").toString().contains("原价合计")
                    ||  mainPd.get("taxTypeName").toString().contains("折扣额合计"))
            ){//
                detailIterator.remove();
                continue;
            }

            //判断是否是本账套数据
            //2024-03-14修改  收票管理  验证税号与购方识别号是否一致。

            if("purchaseInvoiceExcel".equals(business_type)
                    && mainPd.get("buyerCode") !=null && !taxNum.equals(Convert.toDBC(mainPd.get("buyerCode").toString().trim().replace(" ","")))){
                detailIterator.remove();
                continue;
            }else if("saleInvoiceExcel".equals(business_type)
                    && mainPd.get("sellerName") !=null && !companyName.trim().equals(Convert.toDBC(mainPd.get("sellerName").toString().trim().replace(" ","")))){
                //2024-03-14修改   通过名称判断不是该企业的数据时，再增加一个判断，备注中是否记载了代开企业税号，该税号如果和企业一致，则是一条正确数据
                if (mainPd.get("tranmemo") != null && "null".equals(mainPd.get("tranmemo"))){
                    mainPd.put("tranmemo",null);
                }
                if(mainPd.get("tranmemo") !=null && !"".equals(mainPd.get("tranmemo").toString())){
                    String tranmemo = mainPd.get("tranmemo").toString();
                    String chineseTaxId = extractChineseTaxId(tranmemo);
                    if(!chineseTaxId.equals(taxNum)){
                        detailIterator.remove();
                        continue;
                    }
                }else{
                    detailIterator.remove();
                    continue;
                }
            }


            if (mainPd.get("sellerName") !=null && mainPd.get("sellerName").toString().startsWith("国家税务总局")) {
                String tranmemo = mainPd.get("tranmemo") == null?"":mainPd.get("tranmemo").toString();
                String nameReg = "(?<=企业名称:)[^开具]+";
                String codeReg = "(?<=企业税号:)[^代开]+";
                String sellerName = findKeyStringByReg(tranmemo,nameReg);
                String sellerCode = findKeyStringByReg(tranmemo,codeReg);
                mainPd.put("sellerName",sellerName.replace("_x000D_",""));
                mainPd.put("sellerCode",sellerCode);

            }
            //将客户、供应商名称转成半角
            if(mainPd.get("sellerName") !=null){
                mainPd.put("sellerName", Convert.toDBC(mainPd.get("sellerName").toString().trim().replace(" ","")));
            }
            if(mainPd.get("buyerName") !=null){
                mainPd.put("buyerName",Convert.toDBC(mainPd.get("buyerName").toString().trim().replace(" ","")));
            }
            mainData.setAccountCode(accountCode);
            mainData.setUploadPeriod(uploadAccoungntPeriod);
            mainData.setInvoiceCode(mainPd.get("invoiceCode")==null?"":mainPd.get("invoiceCode").toString());
            mainData.setInvoiceNumber(mainPd.get("invoiceNumber")==null?"":mainPd.get("invoiceNumber").toString());
            mainData.setEinvoiceNumber(mainPd.get("einvoiceNumber")==null?"":mainPd.get("einvoiceNumber").toString());
            mainData.setCheckedInvoiceNumber(checkedInvoiceNumber);
            mainData.setSellerCode(mainPd.get("sellerCode") == null?"":mainPd.get("sellerCode").toString());
            mainData.setSellerName(mainPd.get("sellerName") == null?"":mainPd.get("sellerName").toString());
            mainData.setBuyerCode(mainPd.get("buyerCode") == null?"":mainPd.get("buyerCode").toString());
            mainData.setBuyerName(mainPd.get("buyerName") == null?"":mainPd.get("buyerName").toString());
            mainData.setDate(mainPd.get("date") == null?"":mainPd.get("date").toString());

            mainData.setAmount(mainPd.get("amount") == null?"0":mainPd.get("amount").toString());
            mainData.setTaxAmount(mainPd.get("taxAmount") == null?"0":mainPd.get("taxAmount").toString());
            mainData.setInvoiceType(mainPd.get("invoiceType") == null?"":mainPd.get("invoiceType").toString());
            mainData.setInvoiceStatus(mainPd.get("invoiceStatus") == null?"":mainPd.get("invoiceStatus").toString());
            mainData.setInvoiceSource(mainPd.get("invoiceSource") == null?"":mainPd.get("invoiceSource").toString());
            mainData.setIssuer(mainPd.get("issuer") == null?"":mainPd.get("issuer").toString());
            mainData.setTranmemo(mainPd.get("tranmemo") == null?"":mainPd.get("tranmemo").toString());
            mainData.setSpecialType(mainPd.get("specialType") == null?"":mainPd.get("specialType").toString());

            detailData.setAccountCode(accountCode);
            //detailData.setUploadPeriod(uploadAccoungntPeriod);
            detailData.setInvoiceCode(mainPd.get("invoiceCode")==null?"":mainPd.get("invoiceCode").toString());
            detailData.setInvoiceNumber(mainPd.get("invoiceNumber")==null?"":mainPd.get("invoiceNumber").toString());
            detailData.setEinvoiceNumber(mainPd.get("einvoiceNumber")==null?"":mainPd.get("einvoiceNumber").toString());
            detailData.setCheckedInvoiceNumber(checkedInvoiceNumber);
            detailData.setTaxTypeCode(mainPd.get("taxTypeCode") == null?"":mainPd.get("taxTypeCode").toString());
            detailData.setTaxTypeName(mainPd.get("taxTypeName") == null?"":mainPd.get("taxTypeName").toString());
            detailData.setModel(mainPd.get("model") == null?"":mainPd.get("model").toString());
            detailData.setUnit(mainPd.get("unit") == null?"":mainPd.get("unit").toString());
            detailData.setCount(mainPd.get("count") == null?"":mainPd.get("count").toString());
            detailData.setPrice(mainPd.get("price") == null?"":mainPd.get("price").toString());
            detailData.setAmount(mainPd.get("amount") == null?"":mainPd.get("amount").toString());
            detailData.setTaxRate(mainPd.get("taxRate") == null?"":mainPd.get("taxRate").toString());
            detailData.setTaxAmount(mainPd.get("taxAmount") == null?"":mainPd.get("taxAmount").toString());
            detailData.setSpecialType(mainPd.get("specialType") == null?"":mainPd.get("specialType").toString());

            //20240715修改，上海清源商贸账套6月份有一笔收票，只有一个价税合计，没有金额和税额，导致导入错误，所以在此增加判断
            if(StringUtils.isEmpty(mainPd.get("amount"))  && StringUtils.isEmpty(mainPd.get("taxAmount"))
                    && !StringUtils.isEmpty(mainPd.get("taxTotal")) && !StringUtils.isEmpty(checkedInvoiceNumber)){
                mainData.setAmount(mainPd.get("taxTotal").toString());
                mainData.setTaxAmount("0");
                detailData.setAmount(mainPd.get("taxTotal").toString());
                detailData.setTaxAmount("0");
            }

            mx_moneyTotal = CalculateUtil.add(mx_moneyTotal,Double.parseDouble(mainPd.get("amount")==null?"0":mainPd.get("amount").toString()));
            mx_taxmoneyTotal = CalculateUtil.add(mx_taxmoneyTotal,Double.parseDouble(mainPd.get("taxAmount")==null?"0":mainPd.get("taxAmount").toString()));
            mainList.add(mainData);
            detailList.add(detailData);
        }


        if(mainList.isEmpty() || detailList.isEmpty()){

        }
        //遍历基本信息表  //汇总金额  税额。跟汇总信息表中的金额  税额合计比较是否一致，不一致的情况要提示
        if(baseList!=null && !baseList.isEmpty()){
            Iterator<LinkedHashMap> baseIterator = baseList.iterator();
            while (baseIterator.hasNext()) {
                PurchaseInvoice mainData = new PurchaseInvoice();
                PurchaseInvoiceDetail detailData = new PurchaseInvoiceDetail();
                LinkedHashMap mainPd = baseIterator.next();
                //将作废的数据移除
                if(mainPd.get("invoiceStatus") !=null
                        && ("已作废".equals(mainPd.get("invoiceStatus".toString())) || "作废".equals(mainPd.get("invoiceStatus".toString())))){
                    baseIterator.remove();
                    continue;
                }


                //获取符合规则的发票号码
                String checkedInvoiceNumber = getInvoiceNumber(mainPd);
                //如果发票号码不符合规则，移除
                if("".equals(checkedInvoiceNumber)){
                    baseIterator.remove();
                    continue;
                }
                if((mainPd.get("sellerName") ==null
                        || "".equals(mainPd.get("sellerName").toString()))
                        && (mainPd.get("buyerName") ==null
                        || "".equals(mainPd.get("buyerName").toString()))){
                    baseIterator.remove();
                    continue;
                }

                //判断是否是本账套数据
                //2024-03-14修改  收票管理  验证税号与购方识别号是否一致。
                if("purchaseInvoiceExcel".equals(business_type)
                        && mainPd.get("buyerCode") !=null && !taxNum.equals(Convert.toDBC(mainPd.get("buyerCode").toString().trim().replace(" ","")))){
                    baseIterator.remove();
                    continue;
                }else if("saleInvoiceExcel".equals(business_type)
                        && mainPd.get("sellerName") !=null && !companyName.trim().equals(Convert.toDBC(mainPd.get("sellerName").toString().trim().replace(" ","")))){
                    //2024-03-14修改   通过名称判断不是该企业的数据时，再增加一个判断，备注中是否记载了代开企业税号，该税号如果和企业一致，则是一条正确数据
                    if (mainPd.get("tranmemo") != null && "null".equals(mainPd.get("tranmemo"))){
                        mainPd.put("tranmemo",null);
                    }
                    if(mainPd.get("tranmemo") !=null && !"".equals(mainPd.get("tranmemo").toString())){
                        String tranmemo = mainPd.get("tranmemo").toString();
                        String chineseTaxId = extractChineseTaxId(tranmemo);
                        if(!chineseTaxId.equals(taxNum)){
                            baseIterator.remove();
                            continue;
                        }
                    }else{
                        baseIterator.remove();
                        continue;
                    }
                }
                base_moneyTotal = CalculateUtil.add(base_moneyTotal,Double.parseDouble(mainPd.get("amount")==null?"0":mainPd.get("amount").toString()));
                base_taxmoneyTotal = CalculateUtil.add(base_taxmoneyTotal,Double.parseDouble(mainPd.get("taxAmount")==null?"0":mainPd.get("taxAmount").toString()));
            }


            double mx_totalmoney = CalculateUtil.add(mx_moneyTotal,mx_taxmoneyTotal);
            double base_totalmoney = CalculateUtil.add(base_moneyTotal,base_taxmoneyTotal);
            if(mx_totalmoney != base_totalmoney){
                analysisedFile.setState("error");
                analysisedFile.setMessage("检测到所导入的数据中汇总表的金额与基本信息表中的金额不一致，请检查表格数据或联系管理员！");
                return analysisedFile;
            }


            List<PurchaseInvoice> newMainList = mergeItemsWithSameNumber(mainList);

            //处理taxRate和taxTypeName字段  将明细数据中的taxRate和taxTypeName字段 拼接起来赋值给主数据  去除重复数据 使用stream流
            newMainList.stream().forEach(item -> {
                item.setTaxTotal(CalculateUtil.add(item.getAmount(), item.getTaxAmount()));
                item.setTaxRate(detailList.stream().filter(detail -> detail.getCheckedInvoiceNumber().equals(item.getCheckedInvoiceNumber())).map(detail -> detail.getTaxRate()).distinct().collect(Collectors.joining(",")));
                item.setTaxTypeName(detailList.stream().filter(detail -> detail.getCheckedInvoiceNumber().equals(item.getCheckedInvoiceNumber())).map(detail -> detail.getTaxTypeName()).distinct().collect(Collectors.joining(",")));
            });

            //先保存到临时表中
            if(newMainList.isEmpty()){
                analysisedFile.setState("error");
                analysisedFile.setMessage("文件中没有符合条件的数据！");
                return analysisedFile;
            }

            try{
                String token = UUID.randomUUID().toString();
                lock.lock();
                int mainCount = purchaseInvoiceService.batchTempInvoice(accountCode, token, newMainList);
                int detailCount = purchaseInvoiceService.batchTempInvoiceDetail(accountCode, token, detailList);

                // purchaseInvoiceMapper.batchTempInvoice(token,newMainList);

                if("saleInvoiceExcel".equals(business_type)){
                    Map<String, String> mapping = new HashMap<>(1);
                    mapping.put("uploadPeriod", "accountPeriod");
                    List<SaleInvoice> saleInvoices = BeanUtil.copyToList(newMainList, SaleInvoice.class,new CopyOptions().setFieldMapping(mapping));
                    List<SaleInvoiceDetail> saleInvoiceDetails = BeanUtil.copyToList(detailList, SaleInvoiceDetail.class,new CopyOptions().setFieldMapping(mapping));
                    saleInvoiceService.batchInsertInvoice(accountCode,token,saleInvoices);
                    saleInvoiceService.batchInsertInvoiceBackend(Constant.BACKEND_DATABASE,accountCode,token,saleInvoices);

                    saleInvoiceService.batchInsertInvoiceDetail(accountCode,token,saleInvoiceDetails);
                    saleInvoiceService.updateSaleInvoiceMainidByInvoiceNumber(accountCode);
                }else{
                    purchaseInvoiceService.batchInsertInvoice(accountCode,token,newMainList);
                    purchaseInvoiceService.batchInsertInvoiceBackend(Constant.BACKEND_DATABASE,accountCode,token,newMainList);
                    purchaseInvoiceService.batchInsertInvoiceDetail(accountCode,token,detailList);
                    purchaseInvoiceService.updatePurchaseInvoiceMainidByInvoiceNumber(accountCode);

                }
                purchaseInvoiceService.deleteTempDataByToken(accountCode,token);
            }catch (Exception e){
                e.printStackTrace();
            }finally {
                lock.unlock(); // 确保锁被释放
            }


        }
        return analysisedFile;
    }

    public void removeFileToHandled(AnalysisedFile analysisedFile){
        String targetPath = FileConstant.MATERIAL_BROWSER_HANDLED_LOCATION;
        String originPath = analysisedFile.getOriginPath();
        if(originPath.contains(SEPARATOR+"email"+SEPARATOR)){
            targetPath = FileConstant.MATERIAL_EMAIL_HANDLED_LOCATION;
        }else if(originPath.contains(SEPARATOR+"front"+SEPARATOR)){
            targetPath = FileConstant.MATERIAL_FRONT_HANDLED_LOCATION;
        }
        File file = new File(analysisedFile.getOriginPath());
        String name = cn.hutool.core.io.FileUtil.getName(file);
        String suffix = cn.hutool.core.io.FileUtil.getSuffix(name);
        String prefix = cn.hutool.core.io.FileUtil.getPrefix(name);
        String handledFilePath = targetPath + prefix+"-"+ RandomUtil.randomString(5)+"."+suffix;
        if (cn.hutool.core.io.FileUtil.exist(handledFilePath)) {
            handledFilePath = targetPath + RandomUtil.randomString(10) + File.separator + name;
        }
        File newFile = new File(handledFilePath);
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        cn.hutool.core.io.FileUtil.move(file,newFile,true);
        if(file.exists()){
            file.delete();
        }
    }
    private static List<PurchaseInvoice> mergeItemsWithSameNumber(List<PurchaseInvoice> items) {
        return items.stream()
                .collect(Collectors.toMap(
                        PurchaseInvoice::getCheckedInvoiceNumber, // 使用发票号码作为键
                        item -> item, // 将对象作为值
                        (existing, newItem) -> {
                            // 如果有重复的发票号码，合并数量和金额
                            existing.setAmount(CalculateUtil.add(existing.getAmount(),newItem.getAmount(),2));
                            existing.setTaxAmount(CalculateUtil.add(existing.getTaxAmount(),newItem.getTaxAmount(),2));
                            return existing;
                        }
                )).values().stream().collect(Collectors.toList());
    }
    private String findKeyStringByReg(String text,String reg){
        Pattern pattern = Pattern.compile(reg);
        Matcher matcher = pattern.matcher(text);
        if (matcher.find()) {
            String companyName = matcher.group().trim();
            return companyName;
        }
        return "";
    }
    static String extractChineseTaxId(String input) {
        Pattern pattern = Pattern.compile("代开企业税号:(\\w+)");
        Matcher matcher = pattern.matcher(input);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return ""; // Return null if no match is found
    }
    private String getInvoiceNumber(LinkedHashMap mainPd) {
        String invoiceNumber = "";
        String einvoiceNumber = "";
        if(mainPd.get("invoiceNumber")!=null && !"".equals(mainPd.get("invoiceNumber").toString())){
            invoiceNumber = mainPd.get("invoiceNumber").toString().trim().replaceAll(" ","" );
        }
        if(mainPd.get("einvoiceNumber")!=null && !"".equals(mainPd.get("einvoiceNumber").toString())){
            einvoiceNumber = mainPd.get("einvoiceNumber").toString().trim().replaceAll(" ","" );
        }

        if(invoiceNumber.length()>=8 && einvoiceNumber.length()<8){
            return invoiceNumber;
        }else if(invoiceNumber.length()<8 && einvoiceNumber.length()>=8){
            return einvoiceNumber;
        }else if(invoiceNumber.length()>=8 && einvoiceNumber.length()>=8){
            return invoiceNumber.length()-einvoiceNumber.length() >= 0 ?invoiceNumber: einvoiceNumber;
        }else{
            return "";
        }
    }

}

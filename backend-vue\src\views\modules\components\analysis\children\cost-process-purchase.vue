<template>
  <div v-loading="loading" class="table-container">
    <div v-if="currentInvoice && purchaseValue === '3'" class="back-to-summary">
      <el-button type="text" icon="el-icon-arrow-left" @click="backToInvoiceSummary">
        返回发票汇总 ({{ currentInvoice.invoiceNumber }})
      </el-button>
    </div>
    <div v-if="currentSubject && purchaseValue === '4'" class="back-to-summary">
      <el-button type="text" icon="el-icon-arrow-left" @click="backToSubjectSummary">
        返回科目汇总 ({{ currentSubject.subjectName }})
      </el-button>
    </div>
    
    <!-- 隐藏控制区域：当显示发票明细或科目明细时 -->
    <div
      v-if="!currentInvoice && !currentSubject"
      style="margin-top: 0px;margin-bottom: 10px;display: flex; align-items: center;">
      <el-select 
          v-model="selectVal"
          @change="changeSelectVal"
          placeholder="请选择票据类型" 
          style="width:120px"
          :width="80"
           size="mini">
        <el-option
          v-for="item in selectOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value">
        </el-option>
      </el-select>
      <el-select 
          v-model="purchaseValue"
          @change="changeType"
          :width="80"
          placeholder="请选择" style="margin-left: 20px;width:140px" size="mini">
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value">
        </el-option>
      </el-select>
      <el-input
        v-model="searchVal"
        @input="handleSearch"
        placeholder="请输入要查询内容" style="width: 200px;margin-left: 20px;" size="small"></el-input>
     <div style="flex-grow: 1;text-align: right;z-index:2000">{{ saleSituation }}</div>
      <el-button size="small" type="primary" style="z-index:2000"
        @click="handleShowData">查看销售</el-button>
    </div>

      <!-- 发票汇总表 -->
      <invoice-summary-table 
        ref="invoiceSummaryTable"
        v-if="purchaseValue === '3' && !currentInvoice"
        :costData="allCostData"
        :account-nature="account.nature"
        @expand-invoice="handleExpandInvoice"
        @scroll-top="handleInvoiceScrollTop">
      </invoice-summary-table>
      
      <!-- 科目汇总表 -->
      <cost-subject-summary-table 
       ref="costSubjectSummaryTable"
        v-else-if="purchaseValue === '4' && !currentSubject"
        :costData="allCostData"
        :account-nature="account.nature"
        @scroll-top="handleScrollTop"
        @expand-subject="handleExpandSubject">
      </cost-subject-summary-table>
      
      <!-- 详细交易表 -->
      <el-table
        v-else
        ref="purchaseTable"
      :data="purchaseRes" border height="100%">
        <el-table-column prop="type" label="类型" width="80" align="center"></el-table-column>
        <el-table-column prop="personName" label="经手人" width="78" align="center"></el-table-column>
        <el-table-column prop="invoiceType" label="发票类型" width="140" align="center">
          <template slot-scope="scope">
            <div class="text-ellipsis">{{scope.row.invoiceType}}</div>
          </template>
        </el-table-column>
        <el-table-column prop="invoiceNumber" label="发票号码" width="160" align="center">
          <template slot-scope="scope">
              <div class="text-ellipsis">{{scope.row.invoiceNumber}}</div>
          </template>
        </el-table-column>
        <el-table-column 
          :filters="sellerNameFilters"
          :filter-method="filterSellerName"
          prop="sellerName" label="供应商" width="180" 
          align="center">
        <template slot-scope="scope">
            <div class="text-ellipsis">{{scope.row.sellerName}}</div>
        </template>
        </el-table-column>
        <el-table-column prop="taxTypeName" 
        :filters="taxTypeNameFilters"
        :filter-method="filterTaxTypeName"
        filter-placement="bottom-end"
        label="商品名称" align="center">
        <template slot-scope="scope">
            <div class="text-ellipsis">{{scope.row.taxTypeName}}</div>
        </template>
        </el-table-column>
        <el-table-column prop="taxTypeCode" label="税收分类编码" width="130" align="center">
        <template slot-scope="scope">
            <div class="text-ellipsis">{{scope.row.taxTypeCode}}</div>
        </template>
        </el-table-column>
        <el-table-column prop="taxRate" label="税率" width="60" align="center" v-if="account.nature !== '小规模纳税人'"></el-table-column>
        <el-table-column prop="quantity" label="数量" width="60" align="center"></el-table-column>
        <el-table-column prop="amountStr" label="金额" width="120" align="center" v-if="account.nature !== '小规模纳税人'"></el-table-column>
        <el-table-column prop="taxAmountStr" label="税额" width="120" align="center" v-if="account.nature !== '小规模纳税人'"></el-table-column>
        <el-table-column prop="totalAmountStr" :label="account.nature !== '小规模纳税人' ? '价税合计' : '金额'" width="120" align="center"></el-table-column>
        <subject-column
          :account="account"
          label="会计科目"
          placeholder="未识别,请选择"
          @show-subject-select="showSubjectDialog"
          :type="type"
          :manage = "manage">
        </subject-column>
        <el-table-column  label="操作"  
          width="160" align="center"
          :fixed="fixedRightState">
        <template slot-scope="scope">
          <div :style="{ width: '5px',height: '30px',position: 'absolute',top: '9px',left: '-14px',background: scope.row.fixedColor == null ? '#fff' : scope.row.fixedColor}"></div>
    
          <el-tooltip :disabled="ansStatus != 0" :content="hintMsg" placement="top">
            <el-button type="text" class="status-link"
              v-if="scope.row.editable == null || scope.row.editable == false"
              :disabled="ansStatus == 0"
              @click="edit(scope.row,scope.$index,'修改')" size="mini">修改
            </el-button>
          </el-tooltip>

          <el-tooltip :disabled="ansStatus != 0" :content="hintMsg" placement="top">
            <el-button
              type="primary"
              :disabled="ansStatus == 0"
              @click="edit(scope.row,scope.$index)" v-if="scope.row.editable" size="mini">保存
            </el-button>
          </el-tooltip>

          <el-button v-if="scope.row.editable" 
            @click="cancelEdit(scope.row)" size="mini">取消</el-button>

          <el-tooltip :disabled="ansStatus != 0" :content="hintMsg" placement="top">
            <el-button 
              @click="accept(scope.row, purchaseValue, scope.$index)" 
              :disabled="ansStatus == 0"
              v-if="scope.row.opermethodName === 'ai生成' && scope.row.accSubjectId == null"
              type="text" size="small">接受</el-button>
          </el-tooltip>
          <template v-if="scope.row.result === '固定资产' && (scope.row.iscreateFixedcard == null || scope.row.iscreateFixedcard !== 1) 
            && scope.row.amount >= 0 && scope.row.fixedParentid == null && !scope.row.editable">
            <el-tooltip :disabled="ansStatus != 0" :content="hintMsg" placement="top">
              <el-button 
                @click="createFixed(scope.row,scope.$index)" 
                :disabled="ansStatus == 0"
                type="text" 
                size="small">生成资产</el-button>
            </el-tooltip>
          </template>
          <template v-else-if="scope.row.result === '固定资产' && scope.row.iscreateFixedcard === 1">
            <el-tooltip :disabled="ansStatus != 0" :content="hintMsg" placement="top">
              <el-button 
                @click="showFixed(scope.row,scope.$index,'show')" 
                :disabled="ansStatus == 0"
                type="text" 
                size="small">卡片</el-button>
            </el-tooltip>
          </template>
        </template>
        </el-table-column>
    </el-table>
    
    <cost-sale-dialog
      :visible="saleVisible"
      :account="account"
      @update:visible="saleVisible = $event">
    </cost-sale-dialog>
    <subject-select-dialog
      :visible="subject.visible"
      :row="subject.row"
      class-name="cost-subject-select"
      :accountid="account.accountid"
      @update-row="setSubject"
      @update:visible="subject.visible = $event"
      ></subject-select-dialog>
    <fixed-assets-add
      ref="fixedAssetsAdd"
      :visible="fixedAssets.visible"
      v-if="fixedAssets.visible"
      :account="account"
      :data = "fixedAssets.row"
      :type="fixedAssets.type"
      @refresh-data="refreshFixed()"
      @update:visible="fixedAssets.visible = $event"
    ></fixed-assets-add>
    <record-fixed-dialog
      :visible="fixedSelectVisible"
      :row ="currentRow"
      :accountid="account.accountid"
      @update:visible="fixedSelectVisible = $event"
      @update-row="buildFixed"
      >
    </record-fixed-dialog>
  </div>
</template>
<script>
import CostSaleDialog from '../dialog/cost-sale-dialog.vue'
import subjectColumn from './subject-column.vue'
import subjectSelectDialog from '../dialog/subject-select-dialog.vue'
import FixedAssetsAdd from '../dialog/fixed-assets-add.vue'
import RecordFixedDialog from '../dialog/record-fixed-dialog.vue'
import InvoiceSummaryTable from './invoice-summary-table.vue'
import CostSubjectSummaryTable from './cost-subject-summary-table.vue'
import { calculateFixedRight } from '@/utils/tableUtils.js'
import { getAnsStatus } from '@/utils/common.js'

export default {
  data() {
    return {
      purchaseValue: '2',
      purchaseRes: [],
      historyPurchaseRes: [],
      selectOptions: [
        {value: '0', label: '全部'},
        {value: '1', label: '货物类票证'},
        {value: '2', label: '其他票证'}
      ],
      selectVal: '0',
      options: [
        {value: '1', label: '仅显示新增'},
        {value: '2', label: '仅显示待处理'},
        {value: '0', label: '全部'},
        {value: '3', label: '按发票汇总'},
        {value: '4', label: '按科目汇总'}
      ],
      searchVal: '',
      loading: false,
      saleSituation: '',
      saleVisible: false,
      subject: {
        row: {},
        visible: false
      },
      fixedAssets: {
        visible: false,
        row: {},
        type: ''
      },
      oldRow: {},
      currentRow: {},
      fixedSelectVisible: false,
      fixedColor: {},
      manage: {
        nature: '',
        industry: '',
        saleSituation: ''
      },
      fixedRightState: false,
      allCostData: [], // 保存所有数据的副本
      currentInvoice: null, // 当前展开的发票
      currentSubject: null, // 当前展开的科目
      ansStatus: 0, // Add this for status tracking
      hintMsg: '', // Add this for error messages
      subjectScrollTop: 0,
      invoiceScrollTop: 0
    }
  },
  components: { 
    subjectColumn, 
    CostSaleDialog, 
    subjectSelectDialog, 
    FixedAssetsAdd, 
    RecordFixedDialog,
    InvoiceSummaryTable,
    CostSubjectSummaryTable
  },
  props: {
    account: {
      type: Object,
      default: () => {}
    },
    type: {
      type: String,
      default: ''
    }
  },
  computed: {
    sellerNameFilters() {
      const unique = [...new Set(this.purchaseRes.map(item => item.sellerName))];
      // 封装成 el-table 需要的格式
      return unique.map(val => ({ text: val, value: val }));
    },
    taxTypeNameFilters() {
      // 提取唯一 taxTypeName
      const unique = [...new Set(this.purchaseRes.map(item => item.taxTypeName))];
      // 封装成 el-table 需要的格式
      return unique.map(val => ({ text: val, value: val }));
    }
  },
  methods: {
    refreshFixed () {
      this.getCostDetail()
      this.$emit('refresh-msgcount',{type: 'cost'})
    },
    async getCostDetail () {
      this.loading = true
      
      try {
        // 判断是否在查看汇总模式下的特定项目
        const isViewingInvoiceDetail = this.purchaseValue === '3' && this.currentInvoice;
        const isViewingSubjectDetail = this.purchaseValue === '4' && this.currentSubject;
        
        // 在汇总模式下，需要获取所有数据
        const showType = this.selectVal;
        const isNew = this.purchaseValue;
        
        // 请求后端数据
        const {data} = await this.$http({
        url: this.$http.adornUrl('/ans/ansaccountindex/pendingProcess/costDetail'),
        method: 'post',
          data: {accountid: this.account.accountid, showType: showType, isNew: isNew, searchVal: this.searchVal}
        });
        
        if (data && data.code === 0) {
          // 获取完整数据
          const fullResult = data.result;
          
          // 保存历史数据和全部数据副本
          this.historyPurchaseRes = JSON.parse(JSON.stringify(fullResult));
          
          // 如果是汇总模式，也保存全部数据供汇总组件使用
          if (this.purchaseValue === '3' || this.purchaseValue === '4') {
            this.allCostData = JSON.parse(JSON.stringify(fullResult));
          }
          
          // 处理数据显示
          if (isViewingInvoiceDetail) {
            // 按当前选中的发票号过滤数据
            this.purchaseRes = fullResult.filter(item => 
              item.invoiceNumber === this.currentInvoice.invoiceNumber
            );
          } else if (isViewingSubjectDetail) {
            // 按当前选中的会计科目过滤数据
            this.purchaseRes = fullResult.filter(item => 
              item.result === this.currentSubject.subjectName
            );
          } else {
            // 普通模式，显示全部数据
            this.purchaseRes = fullResult;
          }
          
          // 处理固定资产颜色标记
          this.purchaseRes.forEach(item => {
            if (item.result == '固定资产' && item.fixedIds != null && this.fixedColor[item.fixedIds] == null) {
              this.fixedColor[item.fixedIds] = this.getRandomColor()
            }
            if (item.fixedIds != null && this.fixedColor[item.fixedIds] != null) {
              item.fixedColor = this.fixedColor[item.fixedIds]
            }
          });
        }
        
        this.loading = false;
      } catch (error) {
        console.error('获取成本数据失败:', error);
        this.loading = false;
      }
    },
    filterTaxTypeName (value, row) {
      return row.taxTypeName === value;
    },
    filterSellerName (value, row) {
      return row.sellerName === value;
    },
    async getSaleSituation () {
      this.loading = true
      await this.$http({
        url: this.$http.adornUrl('/ans/ansaccountindex/pendingProcess/saleSituation'),
        method: 'post',
        data: {accountid: this.account.accountid}
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.saleSituation = data.result
          let manage = data.manage
          if (manage != null) {
            let manageArray = manage.split(',')
            this.manage.nature = manageArray[0] 
            this.manage.industry = manageArray[1]
            this.manage.principalSales = data.result
          }
        }
        this.loading = false
      }).catch((error) => {
          this.loading = false
        })
    },
    handleShowData () {
      this.saleVisible = true
    },
    fixedRight() {
      this.$nextTick(() => {
        const table = this.$refs.purchaseTable;
        this.fixedRightState = calculateFixedRight(table,260);
      })
    },
    showSubjectDialog (data) {
      this.subject.visible = data.visible
      this.subject.row = data.row
    },
    setSubject (data) {
      this.$set(this.subject.row, 'subjectid', data.id)
      this.$set(this.subject.row, 'result', data.name)
    },
    openCapitalAI(row) {
      if (row) {
        this.$emit('open-ansai', row , 'cost', this.manage)
      }
    },
    edit (row, index, type) {
      if (row.result == '固定资产' && row.iscreateFixedcard != null && row.iscreateFixedcard === 1) {
        this.$confirm('已生成固定资产卡片，是否删除卡片?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.deleteFixedCard(row, index)
        })
        return
      }
      debugger
      if (type == '修改') {
        this.oldRow[row.id] = JSON.parse(JSON.stringify(row))
        this.$set(row, 'editable', true)
      } else {
        this.accept(row, index)
      }
    },
    async deleteFixedCard (row, index) {
      this.loading = true
      await this.$http({
        url: this.$http.adornUrl('/ans/ansaccountindex/deleteFixedCard'),
        method: 'post',
        data: {sourceid: row.id, accountid: this.account.accountid}
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.loading = false
          this.$message({
            message: '操作成功',
            type: 'success'
          })

          if (row.editable == null || row.editable == false) {
            this.$set(row, 'editable', true)
          }
          row.result = ''
          row.subjectid = ''
          row.iscreateFixedcard = 0
        }else {
          this.loading = false
          this.$message.error(data.msg)
        }
      }).catch((error) => {
          this.loading = false
        })
    },
    accept(row , index) {
      if (row.result != null && row.result !== '') {
        this.checkFixed(row , index)
      } else {
        this.$message.warning('会计科目不能为空，请选择')
      }
    },
    checkFixed(row, index) {
      this.currentRow = row;
      if (row.result == '固定资产') {
        let aList = this.purchaseRes.filter(item => item.id != row.id || item.parentid === row.id)
        if (aList.length > 0) {
          this.getFixedInvoiceCount(row, index)
        }else{
          this.acceptCost (row , index)
        }
      } else {
        this.acceptCost (row , index)
      }
    },
    async getFixedInvoiceCount (row, index) {
      this.loading = true
      await this.$http({
        url: this.$http.adornUrl('/ans/ansaccountindex/getFixedInvoiceCount'),
        method: 'post',
        data: {
          accountid: this.account.accountid, 
          id: Number(row.id), taxTypeName: row.taxTypeName
        }
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.loading = false
          if (data.result > 0) {
            this.fixedSelectVisible = true
          } else {
            this.acceptCost (row , index)
          }
        }
      }).catch((error) => {
          this.loading = false
        })
    },
    async buildFixed (data) {
      this.loading = true
      await this.$http({
        url: this.$http.adornUrl('/ans/ansaccountindex/fixedAssets/buildRelationShip'),
        method: 'post',
        data: {
          ids: data.ids.join(','), accountid: this.account.accountid, 
          id: data.id, taxTypeName: this.currentRow.taxTypeName
        }
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.loading = false
          this.$message({
            message: '操作成功',
            type: 'success'
          })
          this.getCostDetail()
        }
      }).catch((error) => {
          this.loading = false
        })
    },
    getRandomColor() {
      const letters = '0123456789ABCDEF';
      let color = '#';
      for (let i = 0; i < 6; i++) {
        color += letters[Math.floor(Math.random() * 16)];
      }
      return color;
    },
    acceptCost (row , index) {
      // 统计同类型税收分类
      if (row.taxTypeName) {
        let list = this.purchaseRes.filter(item => item.taxTypeName === row.taxTypeName)
        if (list.length > 1) {
          this.$confirm('系统检测存在其他同类商品名称, 是否需要自动填充或替换相同商品的归集科目?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            list.forEach(item => {
              item.result = row.result
            })
            let ids = list.map(item => item.id)
            this.acceptAction(ids, row.result)
          }).catch(() => {
            let ids = [row.id]
            this.acceptAction(ids, row.result)     
          })
          return
        }else {
          let ids = [row.id]
          this.acceptAction(ids, row.result)
        }
      }else {
        let ids = [row.id]
        debugger
        this.acceptAction(ids, row.result)
      }
    },
    async acceptAction (ids, result) {
      if (ids.length > 0) {
        this.loading = true
        // 保存当前视图状态
        const currentPurchaseValue = this.purchaseValue
        // 保存当前发票或科目信息
        const currentInvoice = this.currentInvoice ? {...this.currentInvoice} : null
        const currentSubject = this.currentSubject ? {...this.currentSubject} : null
        
        await this.$http({
          url: this.$http.adornUrl('/ans/ansaccountindex/updateResult'),
          method: 'post',
          data: {ids: ids.join(','), dbCode: this.account.accountCode, result: result, type: "cost"}
        }).then(({data}) => {
          this.loading = false
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success'
            })
            
            // 完全重新加载数据
            this.getCostDetail().then(() => {
              // 数据加载完成后，根据视图类型处理
              this.$nextTick(() => {
                debugger
                if (currentPurchaseValue === '3' && currentInvoice) {
                  // 发票汇总视图，找到当前发票数据
                  const updatedInvoice = this.allCostData.filter(item => 
                    item.invoiceNumber === currentInvoice.invoiceNumber
                  )
                  
                  if (updatedInvoice) {
                    updatedInvoice.forEach(item =>{
                      this.$set(item,'editable',false)
                    })
                    this.purchaseRes = updatedInvoice || []
                  }
                } else if (currentPurchaseValue === '4' && currentSubject) {
                  // 科目汇总视图，找到当前科目数据
                  const updatedSubject = this.allCostData.filter(item => 
                    item.subjectName === currentSubject.subjectName
                  )
                  
                  if (updatedSubject) {
                    // this.purchaseRes = currentSubject.items || []
                    if (currentSubject && currentSubject.items) {
                      currentSubject.items.forEach(item => {
                        this.$set(item,'editable',false)
                      })
                      this.purchaseRes = currentSubject.items.filter(item => item.result === currentSubject.subjectName)
                    }
                  }
                }
              })
            })
          } else {
            this.$message({
              message: data.msg,
              type: 'warning'
            })
          }
        }).catch((error) => {
          this.loading = false
        })
      }
    },
    changeType (val) {
      // 重置当前选中的明细项
      this.currentInvoice = null;
      this.currentSubject = null;
      
      if (val === '3' || val === '4') {
        // 如果选择了汇总视图，确保获取全部数据
        const originalValue = this.purchaseValue;
        const originalSelectVal = this.selectVal;
        const showType = this.selectVal;
        const isNew = this.purchaseValue;
        // 构建请求参数并发送请求
        this.$http({
          url: this.$http.adornUrl('/ans/ansaccountindex/pendingProcess/costDetail'),
          method: 'post',
          data: {accountid: this.account.accountid, 
            showType: showType, isNew: isNew,
             searchVal: this.searchVal}
        }).then(({data}) => {
          if (data && data.code === 0) {
            // 保存完整数据用于汇总
            this.allCostData = data.result;
            
            // 恢复原始设置
            this.purchaseValue = originalValue;
            this.selectVal = originalSelectVal;
            
            // 结束加载状态
            this.loading = false;
          }
        }).catch(error => {
          console.error('获取汇总数据失败:', error);
          this.loading = false;
        });
      } else {
        // 普通模式，正常查询
        this.getCostDetail();
      }
    },
    changeSelectVal (val) {
      this.getCostDetail()
    },
    handleCellClick (row, column, cell, event) {
      if (column.property === 'result') {
        this.$set(row, 'editable', true)
      }
    },
    handleSearch (val) {
      this.getCostDetail()
    },
    createFixed (row, index) {
      debugger
      if (row.result != null && row.result == '固定资产') {
        let list = this.purchaseRes.filter(item => {
          if (item.taxTypeName === row.taxTypeName) {
            if (parseFloat(item.amount) < 0){
              let a = parseFloat(item.amount) * -1
              if (a === parseFloat(row.amount)) {
                return true
              }
            }
          }
          return false
        })
        if (list.length > 0) {
          this.$message.warning('系统检测到存在同类商品退货的情况，不能生成固定资产卡片')
          return
        }
        this.$confirm(' 请再次确认是否该采购商品为固定资产。如点击确认，系统将生成固定资产卡片?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let nextRow = {}
          if (this.purchaseRes.length > 0 && this.purchaseRes.length -1 >= index) {
            nextRow = this.purchaseRes[index +1];
          }

          this.showFixed(row, index, 'create',nextRow)
        })
      }
    },
    showFixed (row,index,type,nextRow) {
      if (index >=0) {
        debugger
        let fAmount = 0,tAmount = 0;
        if (nextRow && nextRow.taxTypeName === row.taxTypeName 
          && parseFloat(nextRow.amount) < 0
        ) {
          fAmount = parseFloat(nextRow.amount) * -1
          tAmount = parseFloat(nextRow.totalAmount) * -1
        }else{
          fAmount = 0
          tAmount = 0
        }
        row.sumAmount = row.fixedAmount != null ? row.fixedAmount - fAmount : row.amount - fAmount
        row.sumTotalAmount = row.fixedAmount != null ? row.fixedAmount - tAmount : row.totalAmount - tAmount
      }
      this.fixedAssets.row = row
      this.fixedAssets.visible = true
      this.fixedAssets.type = type
      this.$nextTick(() => {
        this.$refs.fixedAssetsAdd.show()
      })
    },
    cancelEdit(row) {
      // 从历史数据中找到对应的行
      const historyRow = this.historyPurchaseRes.find(item => item.id === row.id)
      if (historyRow) {
        // 恢复所有属性
        Object.keys(historyRow).forEach(key => {
          this.$set(row, key, historyRow[key])
        })
      }
      // 重置编辑状态
      row.editable = false
    },
    // 添加新的方法处理汇总视图
    backToInvoiceSummary() {
      this.currentInvoice = null;
      this.getCostDetail();
      this.$nextTick(() => {
        let sumTable = this.$refs.invoiceSummaryTable
        sumTable.handleBack(this.invoiceScrollTop)
      });
    },
    
    backToSubjectSummary() {
      this.currentSubject = null;
      this.getCostDetail();
      this.$nextTick(() => {
        let sumTable = this.$refs.costSubjectSummaryTable
        sumTable.handleBack(this.subjectScrollTop)
      });
    },
    
    handleExpandInvoice(invoice) {
      this.currentInvoice = invoice;
      this.purchaseRes = invoice.items || [];
    },
    handleScrollTop(scrollTop) {
      this.subjectScrollTop = scrollTop
    },
    handleInvoiceScrollTop(scrollTop) {
      this.invoiceScrollTop = scrollTop
    },
    handleExpandSubject(subject) {
      this.currentSubject = subject;
      this.purchaseRes = subject.items || [];
    },
    async checkAnsStatus() {
      const result = await getAnsStatus(this.$http, this.account.accountid);
      this.ansStatus = result.status;
      this.hintMsg = result.message;
    }
  },
  created() {
    this.checkAnsStatus();
    this.getCostDetail();
    this.getSaleSituation();
  },
  watch: {
    account: {
      handler (val) {
        this.searchVal = ''
        this.selectVal = '0'
        this.purchaseValue = '0'
        this.getCostDetail()
        this.getSaleSituation()
      },
      deep: true
    }
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.fixedRight);
  },
  mounted() {
    this.$nextTick(() => {
      this.fixedRight();
    });
    window.addEventListener('resize', this.fixedRight);
  },
  updated() {
    this.$nextTick(() => {
      this.fixedRight();
    });
  }
}
</script>
<style scoped>

.back-to-summary {
  margin-bottom: 10px;
  padding: 5px 0;
}


.text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>